# هيكل الواجهة الرئيسية والقوائم - سوق الجملة للخضر والغلال

## نظرة عامة على النظام

هذا المستند يوضح الهيكل الشامل والمتطور لنظام إدارة سوق الجملة، والذي يتضمن إدارة متكاملة للحرفاء، الموردين، المشتريات، البضائع، والفواتير.

## 📊 Dashboard - لوحة المعلومات الرئيسية

### المؤشرات الأساسية
- **إجمالي المبيعات**: عرض المبيعات اليومية/الشهرية
- **عدد العمليات**: إحصائية العمليات المنجزة
- **التنبيهات**: تنبيهات المخزون، الديون، المواعيد
- **أبرز الحرفاء**: قائمة بأهم العملاء
- **أبرز الموردين**: قائمة بأهم الموردين

### التصميم المقترح
```html
<div class="dashboard-grid">
    <div class="stats-cards">
        <div class="card sales-card">
            <h3>إجمالي المبيعات</h3>
            <p class="amount">15,750.00 د.ت</p>
        </div>
        <div class="card operations-card">
            <h3>عدد العمليات</h3>
            <p class="count">47 عملية</p>
        </div>
    </div>
    <div class="alerts-section">
        <h3>التنبيهات</h3>
        <ul class="alerts-list">
            <li class="alert warning">مخزون الطماطم منخفض</li>
            <li class="alert info">فاتورة مورد جديدة</li>
        </ul>
    </div>
</div>
```

## 👥 قائمة الحرفاء

### هيكل الجدول
| اسم الحريف | الديون | الصناديق | الرهون | الإجراءات |
|------------|--------|----------|--------|-----------|
| أحمد محمد | 🔴 500 د.ت | 📦 15 | 💰 200 د.ت | عرض/تعديل |

### المميزات
- **البحث الذكي**: بحث فوري بالاسم أو رقم الهاتف
- **الأيقونات التفاعلية**: 
  - 🔴 للديون (أحمر للمتأخرة)
  - 📦 لعدد الصناديق
  - 💰 للرهون
- **فلترة متقدمة**: حسب حالة الدين، المنطقة، نوع العميل

### البيانات المطلوبة
```javascript
const customers = [
    {
        id: 1,
        name: "أحمد محمد",
        phone: "98765432",
        address: "تونس العاصمة",
        debts: 500.00,
        boxes: 15,
        deposits: 200.00,
        status: "active"
    }
];
```

## 🛒 قائمة المشتريات

### نموذج تسجيل المشتريات
```html
<form class="purchase-form">
    <div class="form-row">
        <select name="product-type" required>
            <option>نوع البضاعة</option>
            <option>طماطم</option>
            <option>بطاطا</option>
        </select>
        <select name="supplier" required>
            <option>اسم المورد</option>
        </select>
    </div>
    
    <div class="form-row">
        <input type="number" name="price-per-kg" placeholder="سعر الكيلو" step="0.01">
        <input type="number" name="box-count" placeholder="عدد الصناديق">
        <select name="box-type">
            <option>نوع الصندوق</option>
            <option>الصندوق الكبير</option>
            <option>Plato</option>
        </select>
    </div>
    
    <div class="form-row">
        <input type="number" name="gross-weight" placeholder="الوزن القائم" step="0.1">
        <input type="number" name="net-weight" placeholder="الوزن الصافي (تلقائي)" readonly>
    </div>
    
    <div class="form-row">
        <input type="number" name="total-amount" placeholder="المبلغ الجملي" readonly>
        <input type="number" name="deposit" placeholder="الرهن (تلقائي)" readonly>
        <select name="payment-status">
            <option>حالة الدفع</option>
            <option>كامل</option>
            <option>جزئي</option>
            <option>غير مدفوع</option>
        </select>
    </div>
</form>
```

### جدول المشتريات
| رقم العملية | التاريخ | الحريف | عدد البضائع | الوزن | الحالة | الإجراءات |
|-------------|---------|---------|-------------|-------|---------|-----------|
| #001 | 2025-01-15 | أحمد محمد | 5 أنواع | 150 كغ | مدفوع | تعديل/حذف/تقرير |

### الحسابات التلقائية
```javascript
// حساب الوزن الصافي
function calculateNetWeight(grossWeight, boxCount, boxType) {
    const boxWeights = {
        'الصندوق الكبير': 2.0,
        'Plato': 1.5,
        'Lam plus': 0.75,
        '4 Carro': 0.75,
        'Scarface': 0.75,
        'Lam demi': 0.7,
        'Lam mini': 0.6
    };
    
    const emptyWeight = boxWeights[boxType] * boxCount;
    return grossWeight - emptyWeight;
}

// حساب الرهن
function calculateDeposit(boxCount, boxType) {
    const depositRates = {
        'الصندوق الكبير': 0.200,
        'Plato': 0.200,
        'Lam plus': 0.170,
        '4 Carro': 0.170,
        'Scarface': 0.170,
        'Lam demi': 0.170,
        'Lam mini': 0.170,
        'Carton': 0.300
    };
    
    return boxCount * depositRates[boxType];
}
```

## 🏭 قائمة الموردين

### هيكل الجدول
| اسم المورد | عدد الصناديق المباعة | نوع البضاعة | الوزن الصافي | سعر البيع | الإجراءات |
|------------|-------------------|-------------|-------------|----------|-----------|
| مورد الخضر | 25 صندوق | طماطم، خيار | 180 كغ | 1.2 د.ت/كغ | عرض/تقرير |

### البيانات المحسوبة تلقائياً
```javascript
function calculateSupplierStats(supplierId) {
    const purchases = getPurchasesBySupplier(supplierId);
    
    return {
        totalBoxes: purchases.reduce((sum, p) => sum + p.boxCount, 0),
        productTypes: [...new Set(purchases.map(p => p.productType))],
        totalNetWeight: purchases.reduce((sum, p) => sum + p.netWeight, 0),
        averagePrice: purchases.reduce((sum, p) => sum + p.pricePerKg, 0) / purchases.length
    };
}
```

## 📦 قائمة البضائع

### جدول البضائع اليومية
| النوع | عدد الصناديق | المورد | الوزن الصافي | السعر |
|-------|-------------|---------|-------------|-------|
| طماطم | 15 | مورد الخضر | 85 كغ | 1.2 د.ت |

### جدول الباقي من البضائع
| المورد | النوع | الصناديق المتبقية | الوزن المتبقي |
|---------|-------|-----------------|---------------|
| مورد الخضر | طماطم | 8 | 45 كغ |

### إضافة بضاعة جديدة
```html
<form class="add-product-form">
    <input type="text" name="product-name" placeholder="اسم البضاعة" required>
    <select name="category">
        <option>الفئة</option>
        <option>خضر</option>
        <option>غلال</option>
    </select>
    <input type="number" name="default-price" placeholder="السعر الافتراضي" step="0.01">
</form>
```

## 📋 قائمة الصناديق

### جدول أنواع الصناديق
| النوع | الوزن الفارغ | سعر الحمولة |
|-------|-------------|-------------|
| الصندوق الكبير | 2kg | 0.200dt |
| Plato | 1.5kg | 0.200dt |
| Lam plus | 0.75kg | 0.170dt |
| 4 Carro | 0.75kg | 0.170dt |
| Scarface | 0.75kg | 0.170dt |
| Lam demi | 0.7kg | 0.170dt |
| Lam mini | 0.6kg | 0.170dt |
| Carton | يُدخل وزنه | 0.300dt |
| بلا حمولة | — | الوزن الصافي × 10 |

### بيانات الصناديق
```javascript
const boxTypes = [
    { name: 'الصندوق الكبير', emptyWeight: 2.0, loadPrice: 0.200 },
    { name: 'Plato', emptyWeight: 1.5, loadPrice: 0.200 },
    { name: 'Lam plus', emptyWeight: 0.75, loadPrice: 0.170 },
    { name: '4 Carro', emptyWeight: 0.75, loadPrice: 0.170 },
    { name: 'Scarface', emptyWeight: 0.75, loadPrice: 0.170 },
    { name: 'Lam demi', emptyWeight: 0.7, loadPrice: 0.170 },
    { name: 'Lam mini', emptyWeight: 0.6, loadPrice: 0.170 },
    { name: 'Carton', emptyWeight: 'variable', loadPrice: 0.300 },
    { name: 'بلا حمولة', emptyWeight: 0, loadPrice: 'netWeight * 10' }
];
```

## 🧾 قائمة فواتير الموردين

### نموذج إنشاء الفاتورة
```html
<form class="supplier-invoice-form">
    <div class="form-header">
        <input type="date" name="invoice-date" required>
        <select name="supplier" required>
            <option>اختر المورد</option>
        </select>
    </div>
    
    <div class="invoice-items">
        <div class="item-row">
            <select name="product">
                <option>البضاعة</option>
            </select>
            <input type="number" name="box-count" placeholder="عدد الصناديق">
            <input type="number" name="net-weight" placeholder="الوزن الصافي" step="0.1">
            <input type="number" name="price-per-kg" placeholder="سعر الكيلو" step="0.01">
        </div>
    </div>
    
    <div class="calculations">
        <div class="calc-row">
            <label>المبلغ الخام:</label>
            <span class="gross-amount">0.00 د.ت</span>
        </div>
        <div class="calc-row">
            <label>4% من الخام:</label>
            <span class="four-percent">0.00 د.ت</span>
        </div>
        <div class="calc-row">
            <label>7% من الخام:</label>
            <span class="seven-percent">0.00 د.ت</span>
        </div>
        <div class="calc-row">
            <label>الحمولة:</label>
            <span class="load-cost">0.00 د.ت</span>
        </div>
        <div class="calc-row total">
            <label>الصافي:</label>
            <span class="net-amount">0.00 د.ت</span>
        </div>
    </div>
</form>
```

### حسابات الفاتورة
```javascript
function calculateInvoice(items) {
    let grossAmount = 0;
    let totalLoadCost = 0;
    
    items.forEach(item => {
        grossAmount += item.netWeight * item.pricePerKg;
        totalLoadCost += calculateLoadCost(item.boxCount, item.boxType);
    });
    
    const fourPercent = grossAmount * 0.04;
    const sevenPercent = grossAmount * 0.07;
    const netAmount = grossAmount - (fourPercent + sevenPercent + totalLoadCost);
    
    return {
        grossAmount,
        fourPercent,
        sevenPercent,
        loadCost: totalLoadCost,
        netAmount
    };
}
```

## ⚙️ الإعدادات

### إدارة البيانات الأساسية
- **أسماء البضائع**: إضافة/تعديل/حذف أنواع البضائع
- **أنواع الصناديق**: إدارة الصناديق وأوزانها
- **قائمة الموردين**: إدارة بيانات الموردين
- **كلمات السر**: إدارة الأمان والصلاحيات
- **إعدادات النظام**: تخصيص النظام العام

### واجهة الإعدادات
```html
<div class="settings-tabs">
    <button class="tab-btn active" data-tab="products">البضائع</button>
    <button class="tab-btn" data-tab="boxes">الصناديق</button>
    <button class="tab-btn" data-tab="suppliers">الموردين</button>
    <button class="tab-btn" data-tab="security">الأمان</button>
    <button class="tab-btn" data-tab="system">النظام</button>
</div>
```

---

**ملاحظة**: هذا الهيكل يمثل نظاماً متكاملاً ومتطوراً لإدارة سوق الجملة، مع ترابط كامل بين جميع الأقسام وحسابات تلقائية دقيقة.
