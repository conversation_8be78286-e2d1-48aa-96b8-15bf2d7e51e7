# الملخص التقني الشامل - سوق الجملة للخضر والغلال

## 📋 معلومات المشروع

**اسم البرنامج**: سوق الجملة للخضر والغلال بجرزونة – نقطة بيع عدد 14 – بيه الغالي  
**المالك**: Oussema Souli  
**الواجهة**: عربية – بسيطة واحترافية  
**النظام**: متكامل، جميع الأقسام مرتبطة ببعضها البعض  
**حقوق الملكية**: © 2025 Oussema Souli

## 🎯 المميزات الأساسية

### ⚡ الأداء والسرعة
- تسريع النظام والعمليات الحسابية
- دعم نظام حساب تلقائي متقدم
- استجابة فورية للمدخلات
- تحديث البيانات في الوقت الفعلي

### 💾 إدارة البيانات
- دعم الحفظ الدائم والتلقائي
- نظام تاريخ دقيق بالثواني
- تنسيق زمني ذكي (اليوم، الأمس)
- نسخ احتياطية تلقائية

### 🎨 التصميم والواجهة
- تصميم عصري بالكروت والأيقونات
- جداول ذكية وتفاعلية
- واجهة سهلة التنقل
- قائمة جانبية عمودية على يمين الشاشة

### 🔧 الوظائف المتقدمة
- إمكانية تعديل وحذف العمليات
- استخراج التقارير والوصولات
- الوصول السريع للبيانات
- نظام بحث ذكي متقدم

## 🧭 الهيكل التنظيمي للأقسام

### القائمة الجانبية العمودية (يمين الشاشة)
```
📊 Dashboard
👥 الحرفاء  
🛒 المشتريات
🏭 الموردين
📦 البضائع
📋 الصناديق
🧾 فواتير الموردين
⚙️ الإعدادات
```

## 📊 تفاصيل الأقسام

### 1. 👥 قسم الحرفاء

#### المميزات:
- جدول شامل بجميع الحرفاء
- نظام بحث ذكي ومتقدم
- أيقونات تفاعلية للحالات

#### الأيقونات والمؤشرات:
- 💰 **الديون**: عرض المبالغ المستحقة
- 📦 **الصناديق**: عدد الصناديق المستخدمة
- 🔐 **الرهون**: قيمة الرهون المحجوزة

#### الترابط:
- كل حريف مرتبط بجميع عملياته
- تحديث تلقائي للبيانات مع كل عملية

### 2. 🏭 قسم الموردين

#### هيكل الجدول:
| اسم المورد | عدد الصناديق المباعة | نوع البضاعة | الوزن الصافي | سعر البيع |
|------------|-------------------|-------------|-------------|----------|

#### المميزات:
- استخراج البيانات تلقائياً من المشتريات
- حساب إحصائيات شاملة لكل مورد
- ربط مباشر مع فواتير الموردين

### 3. 🛒 قسم المشتريات

#### نظام التسجيل المتكامل:

**الجدول الأفقي يتضمن**:
- نوع البضاعة
- اسم المورد  
- سعر الكيلو
- عدد الصناديق
- نوع الصندوق
- الوزن القائم
- **الوزن الصافي** (يُحسب تلقائياً)
- **المبلغ الجملي** (يُحسب تلقائياً)

#### التعمير الذكي:
- بحث مباشر عند كتابة أول حرف من اسم الحريف
- اقتراحات تلقائية للبيانات
- تكملة الحقول بناءً على التاريخ السابق

#### الخيارات المتقدمة:
- ✅ **Checkbox**: حساب الرهن تلقائياً
- ✅ **Checkbox**: تفعيل خيارات الدفع (جزئي/كلي/عدم الدفع)

#### الإجراءات:
- حفظ تلقائي في "قائمة المشتريات"
- إمكانية تعديل/حذف/عرض تقرير
- استخراج وصل فوري

### 4. 📦 قسم البضائع

#### الجداول المتعددة:

**الجدول الأول**: البضائع الموردة اليوم
| اسم البضاعة | عدد الصناديق | المورد |
|-------------|-------------|---------|

**الجدول الثاني**: باقي البضائع المتوفرة
- تصنيف حسب كل مورد
- عرض الكميات المتبقية
- تتبع حالة المخزون

#### المميزات:
- إمكانية إضافة بضائع جديدة
- نظام بحث متقدم
- تحديث تلقائي مع العمليات

### 5. 📋 قسم الصناديق

#### جدول أنواع الصناديق:

| نوع الصندوق | الوزن الفارغ | الحمولة |
|-------------|-------------|---------|
| الصندوق الكبير | 2kg | 0.200dt |
| Plato | 1.5kg | 0.200dt |
| Lam plus | 0.75kg | 0.170dt |
| 4 Carro | 0.75kg | 0.170dt |
| Scarface | 0.75kg | 0.170dt |
| Lam demi | 0.7kg | 0.170dt |
| Lam mini | 0.6kg | 0.170dt |
| Carton | يُدخل يدوياً | 0.300dt |
| **بلا حمولة** | — | **الوزن الصافي × 0.01dt** |

#### ملاحظات مهمة:
- الحمولة تُستخدم **فقط** في فواتير الموردين
- نظام "بلا حمولة" له حساب خاص
- تحديث تلقائي لإحصائيات الاستخدام

### 6. 🧾 قسم فواتير الموردين

#### عملية إنشاء الفاتورة:

**الخطوة الأولى**: إدخال البيانات الأساسية
- التاريخ
- اسم المورد

**الخطوة الثانية**: جدول البضائع
- عدد الصناديق
- الوزن الصافي  
- السعر للكيلو

#### الحسابات التلقائية:
```
المبلغ الخام = الوزن الصافي × السعر
خصم 4% = المبلغ الخام × 0.04
خصم 7% = المبلغ الخام × 0.07
الحمولة = حسب نوع الصندوق أو الوزن
المبلغ الصافي = الخام - (الخصومات + الحمولة)
```

#### المميزات المتقدمة:
- 📄 **زر "استخراج الفواتير"**: عرض جميع فواتير التاريخ
- قائمة منفصلة لكل مورد
- إمكانية تعديل الوزن أو البضاعة قبل الطباعة
- **التأثير المباشر**: أي تعديل ينعكس على جميع الأقسام

### 7. ⚙️ قسم الإعدادات

#### إدارة النظام:
- إدارة المستخدمين والصلاحيات
- إعداد معدلات الحمولة
- إعداد نسب الضرائب والخصومات
- ربط وتزامن البيانات

#### الأمان والنسخ الاحتياطية:
- إدارة كلمات المرور
- جدولة النسخ الاحتياطية
- استعادة البيانات
- تصدير واستيراد البيانات

## 🔗 نظام الترابط المتكامل

### التأثيرات المتبادلة:
```
المشتريات → الحرفاء (الديون، الصناديق، الرهون)
المشتريات → الموردين (الإحصائيات، البيانات)
المشتريات → البضائع (المخزون، التوفر)
فواتير الموردين → جميع الأقسام (التحديث الشامل)
```

### التحديث التلقائي:
- كل عملية تؤثر فوراً على الأقسام ذات الصلة
- حفظ تلقائي مع كل تغيير
- تزامن البيانات في الوقت الفعلي

## 🧮 نظام الحسابات التلقائية

### حسابات المشتريات:
```javascript
الوزن الصافي = الوزن القائم - (عدد الصناديق × وزن الصندوق الفارغ)
المبلغ الجملي = الوزن الصافي × سعر الكيلو
الرهن = عدد الصناديق × معدل الرهن للصندوق
```

### حسابات "بلا حمولة":
```javascript
الرهن = الوزن الصافي × 0.01
```

### حسابات فواتير الموردين:
```javascript
المبلغ الخام = مجموع (الوزن الصافي × السعر) لكل بضاعة
خصم 4% = المبلغ الخام × 0.04
خصم 7% = المبلغ الخام × 0.07
الحمولة = مجموع (عدد الصناديق × معدل الحمولة) لكل نوع
المبلغ الصافي = المبلغ الخام - خصم 4% - خصم 7% - الحمولة
```

## 🚀 خيارات التطبيق

### 🖥️ تطبيق مكتبي (Desktop App)
**التقنيات المقترحة**:
- **Python + Tkinter**: سهولة التطوير والصيانة
- **Electron**: واجهة ويب بأداء مكتبي
- **Qt/PyQt**: واجهة احترافية ومتقدمة

**المميزات**:
- أداء عالي وسرعة في الاستجابة
- عمل بدون اتصال إنترنت
- أمان عالي للبيانات المحلية
- سهولة التثبيت والصيانة

### 🌐 تطبيق ويب (Web App)
**التقنيات المقترحة**:
- **Frontend**: HTML5/CSS3/JavaScript (ES6+)
- **Backend**: Node.js/Express أو Python/Flask
- **قاعدة البيانات**: SQLite أو PostgreSQL
- **التخزين المحلي**: LocalStorage + IndexedDB

**المميزات**:
- إمكانية الوصول من أي جهاز
- تحديثات تلقائية
- مشاركة البيانات بين المستخدمين
- نسخ احتياطية سحابية

## 📈 التوصية التقنية

### للبداية: **تطبيق ويب متقدم**
- سهولة التطوير والاختبار
- مرونة في التحديث والصيانة
- إمكانية التوسع المستقبلي
- دعم الأجهزة المختلفة

### للمستقبل: **تطبيق مكتبي مصاحب**
- للاستخدام بدون إنترنت
- أداء محسن للعمليات الكثيفة
- نسخة محمولة للأجهزة اللوحية

---

**الخطوة التالية**: بدء تطوير النموذج الأولي للتطبيق الويب مع التركيز على الوظائف الأساسية والترابط بين الأقسام.
