# الوثائق التقنية - سوق الجملة للخضر والغلال

## هيكل المشروع

```
سوق_الجملة/
├── index.html          # الصفحة الرئيسية
├── app.js             # منطق التطبيق الرئيسي
├── data.js            # البيانات الأساسية والمتغيرات
├── style.css          # ملف التصميم
└── تقرير_شامل_سوق_الجملة.md  # التقرير الشامل
```

## تحليل الملفات

### 1. index.html
**الغرض**: هيكل الصفحة الأساسي والعناصر الرئيسية

**العناصر الرئيسية**:
- `<header>`: عنوان السوق ونقطة البيع
- `#dashboard`: لوحة المعلومات (المبيعات اليومية وعدد المنتجات)
- `#operations`: منطقة العمليات مع التبويبات
- `<footer>`: معلومات المطور

**التبويبات المتاحة**:
- `products`: إدارة المنتجات
- `sales`: عمليات البيع
- `inventory`: المخزون (غير مفعل حاليًا)
- `reports`: التقارير (غير مفعل حاليًا)

### 2. data.js
**الغرض**: تخزين البيانات الأساسية والمتغيرات العامة

**المتغيرات الرئيسية**:
```javascript
const products = [...]      // مصفوفة المنتجات
const categories = [...]    // الفئات المتاحة
const units = [...]         // وحدات القياس
let dailySales = []         // المبيعات اليومية
let currentTab = 'products' // التبويب الحالي
let selectedProduct = null  // المنتج المحدد للتعديل
```

### 3. app.js
**الغرض**: منطق التطبيق والوظائف التفاعلية

**الوظائف الرئيسية**:

#### `updateDashboard()`
- تحديث عدد المنتجات
- حساب إجمالي المبيعات اليومية
- تحديث عرض لوحة المعلومات

#### `renderProducts()`
- عرض نموذج إضافة/تعديل المنتجات
- عرض جدول المنتجات الحالية
- معالجة أحداث الإضافة والتعديل والحذف

#### `renderSales()`
- عرض واجهة البيع
- إدارة عربة التسوق
- معالجة عمليات البيع

### 4. style.css
**الغرض**: تصميم وتنسيق الواجهة

**المتغيرات CSS**:
```css
:root {
    --primary-color: #2e7d32;    /* الأخضر الأساسي */
    --secondary-color: #ff9800;   /* البرتقالي الثانوي */
    --accent-color: #4caf50;      /* الأخضر المميز */
    --text-color: #333;           /* لون النص */
    --light-bg: #f5f5f5;          /* الخلفية الفاتحة */
    --card-shadow: 0 4px 8px rgba(0,0,0,0.1); /* ظل البطاقات */
}
```

## تدفق البيانات

### 1. تحميل التطبيق
```
تحميل الصفحة → تحميل البيانات → تهيئة الواجهة → عرض المنتجات
```

### 2. إضافة منتج جديد
```
ملء النموذج → التحقق من البيانات → إضافة للمصفوفة → تحديث الواجهة → تحديث لوحة المعلومات
```

### 3. عملية بيع
```
اختيار المنتج → تحديد الكمية → إضافة للفاتورة → إتمام البيع → تحديث المخزون → تسجيل المبيعات
```

## الوظائف التفصيلية

### إدارة المنتجات

#### إضافة منتج:
```javascript
// التحقق من البيانات
const name = document.getElementById('product-name').value;
const category = document.getElementById('product-category').options[selectedIndex].text;
const unit = document.getElementById('product-unit').value;
const price = parseFloat(document.getElementById('product-price').value);
const stock = parseInt(document.getElementById('product-stock').value);

// إنشاء معرف جديد
const newId = Math.max(...products.map(p => p.id), 0) + 1;

// إضافة المنتج
products.push({ id: newId, name, category, unit, price, stock });
```

#### تعديل منتج:
```javascript
// العثور على المنتج
const index = products.findIndex(p => p.id == id);

// تحديث البيانات
if (index !== -1) {
    products[index] = { id: parseInt(id), name, category, unit, price, stock };
}
```

#### حذف منتج:
```javascript
// تأكيد الحذف
if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
    products = products.filter(p => p.id != id);
}
```

### عمليات البيع

#### إضافة منتج للفاتورة:
```javascript
// التحقق من توفر الكمية
if (product.stock < quantity) {
    alert(`الكمية المتاحة غير كافية! المخزون الحالي: ${product.stock}`);
    return;
}

// إضافة أو تحديث الكمية
const existingItem = cart.find(item => item.id == productId);
if (existingItem) {
    existingItem.quantity += quantity;
} else {
    cart.push({ id, name, unit, price, quantity });
}
```

#### إتمام البيع:
```javascript
// تحديث المخزون
cart.forEach(item => {
    const product = products.find(p => p.id == item.id);
    if (product) {
        product.stock -= item.quantity;
    }
});

// تسجيل المبيعات
dailySales.push({
    date: new Date(),
    items: [...cart],
    total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)
});
```

## معالجة الأحداث

### التبويبات:
```javascript
tabBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        // إزالة النشاط من جميع الأزرار
        tabBtns.forEach(b => b.classList.remove('active'));
        
        // إخفاء جميع المحتويات
        tabContents.forEach(content => content.style.display = 'none');
        
        // تفعيل الزر المحدد
        btn.classList.add('active');
        const tabId = btn.getAttribute('data-tab');
        document.getElementById(`${tabId}-content`).style.display = 'block';
    });
});
```

### النماذج:
```javascript
document.getElementById('product-form').addEventListener('submit', function(e) {
    e.preventDefault();
    // معالجة البيانات
});
```

## التحسينات المقترحة

### 1. تحسين الأداء
- استخدام `DocumentFragment` لتحديث DOM
- تحسين عمليات البحث في المصفوفات
- إضافة تخزين محلي (localStorage)

### 2. تحسين تجربة المستخدم
- إضافة رسائل تأكيد للعمليات الناجحة
- تحسين رسائل الخطأ
- إضافة مؤشرات التحميل

### 3. إضافة وظائف جديدة
- البحث والتصفية
- الترتيب حسب معايير مختلفة
- تصدير البيانات

## اختبار النظام

### اختبارات أساسية:
1. **إضافة منتج**: التأكد من إضافة المنتج بنجاح
2. **تعديل منتج**: التأكد من حفظ التغييرات
3. **حذف منتج**: التأكد من الحذف مع التأكيد
4. **عملية بيع**: التأكد من تحديث المخزون والمبيعات
5. **التحقق من الكمية**: منع البيع عند عدم توفر الكمية

### اختبارات متقدمة:
1. **التوافق مع المتصفحات**: اختبار على متصفحات مختلفة
2. **الاستجابة**: اختبار على أحجام شاشات مختلفة
3. **الأداء**: اختبار مع كميات كبيرة من البيانات

## الأمان

### الإجراءات الحالية:
- التحقق من صحة المدخلات
- رسائل تأكيد للعمليات الحساسة
- منع القيم السالبة للأسعار والكميات

### تحسينات مقترحة:
- تشفير البيانات الحساسة
- إضافة نظام مصادقة
- تسجيل العمليات (Logging)

## الصيانة

### مهام دورية:
1. **نسخ احتياطية**: حفظ البيانات بانتظام
2. **تحديث المتصفح**: التأكد من التوافق
3. **مراجعة الأداء**: فحص سرعة الاستجابة
4. **تحديث البيانات**: مراجعة الأسعار والمنتجات

---

**إعداد**: أسامة الصولي  
**آخر تحديث**: 2025  
**الإصدار**: 1.0
