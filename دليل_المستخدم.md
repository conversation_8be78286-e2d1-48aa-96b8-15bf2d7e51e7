# دليل المستخدم - سوق الجملة للخضر والغلال

## مقدمة

مرحباً بك في نظام إدارة سوق الجملة للخضر والغلال - نقطة بيع عدد 14 - بيه الغالي. هذا الدليل سيساعدك على استخدام النظام بكفاءة وفعالية.

## المميزات الرئيسية للنظام

### 🚀 الأداء والسرعة
- **حساب تلقائي فوري**: جميع العمليات الحسابية تتم تلقائياً دون تدخل منك
- **استجابة سريعة**: النظام يتفاعل مع إجراءاتك في الحال
- **معالجة محسنة**: عمليات سريعة حتى مع كميات كبيرة من البيانات

### 🎨 التصميم الاحترافي
- **جداول منظمة**: عرض البيانات في جداول واضحة ومرتبة
- **كروت تفاعلية**: معلومات منظمة في بطاقات جميلة
- **أيقونات واضحة**: رموز بصرية تسهل الفهم والاستخدام
- **ألوان متناسقة**: نظام ألوان مريح للعين ومناسب للعمل الطويل

### 🔗 التكامل الذكي
- **ترابط الأقسام**: كل تغيير في قسم يؤثر تلقائياً على الأقسام الأخرى
- **تحديث فوري**: المعلومات تتحدث في جميع أنحاء النظام فوراً
- **تزامن البيانات**: لا حاجة لإعادة تحميل أو تحديث يدوي

### 💾 إدارة البيانات المتقدمة
- **حفظ دائم**: بياناتك محفوظة بشكل دائم وآمن
- **تاريخ دقيق**: تسجيل زمني دقيق لكل عملية إلى مستوى الثواني
- **تصنيف زمني**: تمييز العمليات حسب "اليوم" و"الأمس" تلقائياً

### 📱 واجهة متطورة
- **قوائم جانبية**: قوائم عمودية على الجانب الأيمن مناسبة للعربية
- **تصميم متجاوب**: يعمل بكفاءة على جميع الأجهزة
- **سهولة التنقل**: واجهة سلسة وبديهية

## البدء السريع

### 1. فتح النظام
- افتح ملف `index.html` في أي متصفح ويب حديث
- ستظهر الواجهة الرئيسية مع لوحة المعلومات

### 2. فهم الواجهة الرئيسية
الواجهة مقسمة إلى أقسام رئيسية:
- **العنوان**: اسم السوق ونقطة البيع
- **لوحة المعلومات**: عرض المبيعات اليومية وعدد المنتجات
- **التبويبات**: إدارة المنتجات وعمليات البيع
- **المحتوى**: منطقة العمل الرئيسية

## إدارة المنتجات

### إضافة منتج جديد

1. **انقر على تبويب "إدارة المنتجات"** (مفعل افتراضياً)
2. **املأ نموذج المنتج الجديد**:
   - **اسم المنتج**: أدخل اسم المنتج (مثل: طماطم، تفاح)
   - **الفئة**: اختر من القائمة (خضر، غلال، بهارات، أخرى)
   - **وحدة القياس**: اختر الوحدة (كيلو، صندوق، كرتونة، علبة، حبة)
   - **السعر**: أدخل السعر بالدينار التونسي
   - **الكمية المتاحة**: أدخل الكمية في المخزون
3. **انقر على زر "إضافة"**
4. **ستظهر رسالة تأكيد** وسيتم إضافة المنتج للقائمة

### تعديل منتج موجود

1. **ابحث عن المنتج** في جدول المنتجات
2. **انقر على زر "تعديل"** بجانب المنتج المطلوب
3. **ستظهر بيانات المنتج** في النموذج العلوي
4. **عدّل البيانات** حسب الحاجة
5. **انقر على زر "تحديث"** لحفظ التغييرات
6. **أو انقر على "إلغاء"** للتراجع عن التعديل

### حذف منتج

1. **ابحث عن المنتج** في جدول المنتجات
2. **انقر على زر "حذف"** بجانب المنتج
3. **ستظهر رسالة تأكيد** - انقر "موافق" للمتابعة أو "إلغاء" للتراجع
4. **سيتم حذف المنتج** من النظام نهائياً

⚠️ **تحذير**: عملية الحذف لا يمكن التراجع عنها!

## عمليات البيع

### إنشاء فاتورة جديدة

1. **انقر على تبويب "عمليات البيع"**
2. **اختر المنتج** من القائمة المنسدلة
3. **أدخل الكمية المطلوبة**
4. **انقر على "إضافة إلى الفاتورة"**

### إدارة الفاتورة

#### إضافة منتجات متعددة:
- كرر عملية إضافة المنتجات حسب الحاجة
- ستظهر جميع المنتجات في جدول الفاتورة
- سيتم حساب المجموع تلقائياً

#### حذف منتج من الفاتورة:
- انقر على زر "✕" بجانب المنتج المراد حذفه
- سيتم إزالة المنتج وإعادة حساب المجموع

### إتمام عملية البيع

1. **تأكد من صحة الفاتورة** ومراجعة جميع المنتجات
2. **انقر على زر "إتمام البيع"**
3. **ستظهر رسالة تأكيد** بنجاح العملية
4. **سيتم تحديث المخزون** تلقائياً
5. **ستُسجل العملية** في المبيعات اليومية
6. **ستُصفر الفاتورة** للعملية التالية

## لوحة المعلومات

### المبيعات اليومية
- تعرض إجمالي مبيعات اليوم بالدينار التونسي
- تتحدث تلقائياً مع كل عملية بيع
- **حساب تلقائي**: المجموع يُحسب فوراً دون تدخل منك
- **دقة زمنية**: تسجيل دقيق لوقت كل عملية بيع

### المنتجات المتاحة
- تعرض العدد الإجمالي للمنتجات في النظام
- تتحدث عند إضافة أو حذف منتجات
- **تحديث فوري**: العدد يتغير مباشرة مع أي تعديل

## الاستفادة من المميزات المتقدمة

### 🧮 النظام الحسابي التلقائي
**كيف يعمل**:
- عند إضافة منتج للفاتورة، يُحسب المجموع تلقائياً
- تغيير الكمية يعيد حساب المجموع فوراً
- إزالة منتج تحدث المجموع تلقائياً

**فائدة لك**: توفير الوقت ومنع أخطاء الحساب

### 🔄 الترابط بين الأقسام
**أمثلة على الترابط**:
- بيع منتج ← تقليل المخزون ← تحديث المبيعات ← تحديث لوحة المعلومات
- إضافة منتج جديد ← ظهوره في قائمة البيع ← تحديث عدد المنتجات
- تعديل سعر ← تحديث فوري في جميع أنحاء النظام

**فائدة لك**: عدم الحاجة لتحديث كل قسم يدوياً

### 📅 نظام التاريخ الذكي
**المميزات**:
- تسجيل دقيق لوقت كل عملية (سنة، شهر، يوم، ساعة، دقيقة، ثانية)
- تصنيف تلقائي للعمليات: "اليوم" و"الأمس"
- إمكانية تتبع العمليات بدقة زمنية عالية

**فائدة لك**: مراجعة دقيقة للعمليات وتحليل الأداء

### 🎨 التصميم التفاعلي
**العناصر التفاعلية**:
- **الكروت**: تتفاعل عند التمرير عليها
- **الجداول**: ترتيب وتنظيم واضح للبيانات
- **الأيقونات**: رموز واضحة لكل عملية
- **الألوان**: نظام ألوان يميز بين أنواع العمليات

**فائدة لك**: تجربة استخدام ممتعة وسهلة

### 💾 الحفظ الدائم
**كيف يعمل**:
- جميع البيانات تُحفظ تلقائياً
- لا حاجة للضغط على "حفظ"
- البيانات محفوظة حتى لو أُغلق المتصفح

**فائدة لك**: أمان كامل للبيانات وعدم فقدان أي معلومة

## نصائح مهمة

### ✅ أفضل الممارسات

1. **تحديث الأسعار بانتظام**: راجع أسعار المنتجات دورياً
2. **مراقبة المخزون**: تابع كميات المنتجات لتجنب النفاد
3. **التحقق من البيانات**: تأكد من صحة البيانات قبل الحفظ
4. **النسخ الاحتياطية**: احفظ نسخة من البيانات بانتظام

### ⚠️ تحذيرات مهمة

1. **لا تغلق المتصفح** أثناء عملية البيع
2. **تأكد من الكمية** قبل إتمام البيع
3. **راجع الفاتورة** قبل الإتمام
4. **احفظ البيانات** بانتظام

## حل المشاكل الشائعة

### المشكلة: لا يمكن إضافة منتج للفاتورة
**الحل**: 
- تأكد من اختيار منتج من القائمة
- تأكد من أن الكمية المطلوبة متوفرة في المخزون
- تأكد من إدخال كمية صحيحة (رقم موجب)

### المشكلة: لا يظهر المنتج في قائمة البيع
**الحل**:
- تأكد من إضافة المنتج في قسم "إدارة المنتجات" أولاً
- تأكد من أن المنتج له مخزون متاح

### المشكلة: الأسعار غير صحيحة
**الحل**:
- انتقل لقسم "إدارة المنتجات"
- عدّل سعر المنتج المطلوب
- احفظ التغييرات

### المشكلة: النظام بطيء
**الحل**:
- أغلق التبويبات الأخرى في المتصفح
- أعد تحميل الصفحة
- تأكد من أن المتصفح محدث

## الاختصارات المفيدة

- **Tab**: للانتقال بين الحقول
- **Enter**: لإرسال النموذج
- **Escape**: لإلغاء العمليات
- **F5**: لإعادة تحميل الصفحة

## الدعم الفني

### للحصول على المساعدة:
- **المطور**: أسامة الصولي
- **البريد الإلكتروني**: <EMAIL>

### قبل طلب المساعدة:
1. تأكد من اتباع الخطوات في هذا الدليل
2. جرب إعادة تحميل الصفحة
3. تحقق من رسائل الخطأ إن وجدت
4. اكتب وصفاً مفصلاً للمشكلة

## التحديثات المستقبلية

### مميزات قادمة:
- إدارة المخزون المتقدمة
- نظام التقارير التفصيلية
- طباعة الفواتير
- إدارة العملاء
- النسخ الاحتياطية التلقائية

### كيفية الحصول على التحديثات:
- تابع مع المطور للحصول على الإصدارات الجديدة
- احتفظ بنسخة احتياطية من بياناتك قبل التحديث

## خاتمة

هذا النظام مصمم ليكون بسيطاً وفعالاً لإدارة عمليات البيع اليومية. مع الاستخدام المنتظم، ستجد أنه يوفر الوقت والجهد ويقلل من الأخطاء.

نتمنى لك تجربة ممتعة ومفيدة مع النظام!

---

**إعداد**: أسامة الصولي  
**التاريخ**: 2025  
**الإصدار**: 1.0
