# دليل الحسابات والمعادلات - سوق الجملة للخضر والغلال

## نظرة عامة

هذا الدليل يوضح جميع المعادلات والحسابات التلقائية المستخدمة في النظام، مع أمثلة عملية وحالات خاصة.

## 🧮 حسابات المشتريات الأساسية

### 1. حساب الوزن الصافي

**المعادلة الأساسية**:
```
الوزن الصافي = الوزن القائم - (عدد الصناديق × وزن الصندوق الفارغ)
```

**مثال عملي**:
```
الوزن القائم = 100 كغ
عدد الصناديق = 10
نوع الصندوق = "الصندوق الكبير" (وزن فارغ = 2 كغ)

الوزن الصافي = 100 - (10 × 2) = 100 - 20 = 80 كغ
```

**جدول أوزان الصناديق**:
| نوع الصندوق | الوزن الفارغ (كغ) |
|-------------|------------------|
| الصندوق الكبير | 2.0 |
| Plato | 1.5 |
| Lam plus | 0.75 |
| 4 Carro | 0.75 |
| Scarface | 0.75 |
| Lam demi | 0.7 |
| Lam mini | 0.6 |
| Carton | متغير (يُدخل يدوياً) |

### 2. حساب المبلغ الجملي

**المعادلة**:
```
المبلغ الجملي = الوزن الصافي × سعر الكيلو
```

**مثال**:
```
الوزن الصافي = 80 كغ
سعر الكيلو = 1.5 د.ت

المبلغ الجملي = 80 × 1.5 = 120.00 د.ت
```

### 3. حساب الرهن (الحالة العادية)

**المعادلة**:
```
الرهن = عدد الصناديق × معدل الرهن للصندوق
```

**جدول معدلات الرهن**:
| نوع الصندوق | معدل الرهن (د.ت) |
|-------------|------------------|
| الصندوق الكبير | 0.200 |
| Plato | 0.200 |
| Lam plus | 0.170 |
| 4 Carro | 0.170 |
| Scarface | 0.170 |
| Lam demi | 0.170 |
| Lam mini | 0.170 |
| Carton | 0.300 |

**مثال**:
```
عدد الصناديق = 10
نوع الصندوق = "الصندوق الكبير"
معدل الرهن = 0.200 د.ت

الرهن = 10 × 0.200 = 2.00 د.ت
```

### 4. حساب الرهن (حالة "بلا حمولة")

**المعادلة الخاصة**:
```
الرهن = الوزن الصافي × 0.01
```

**مثال**:
```
الوزن الصافي = 80 كغ
نوع الصندوق = "بلا حمولة"

الرهن = 80 × 0.01 = 0.80 د.ت
```

### 5. المجموع النهائي للمشتريات

**المعادلة**:
```
المجموع النهائي = المبلغ الجملي + الرهن
```

**مثال كامل**:
```
الوزن القائم = 100 كغ
عدد الصناديق = 10
نوع الصندوق = "الصندوق الكبير"
سعر الكيلو = 1.5 د.ت

الوزن الصافي = 100 - (10 × 2) = 80 كغ
المبلغ الجملي = 80 × 1.5 = 120.00 د.ت
الرهن = 10 × 0.200 = 2.00 د.ت
المجموع النهائي = 120.00 + 2.00 = 122.00 د.ت
```

## 💰 حسابات فواتير الموردين

### 1. حساب المبلغ الخام

**المعادلة**:
```
المبلغ الخام = مجموع (الوزن الصافي × سعر الكيلو) لكل بضاعة
```

**مثال متعدد البضائع**:
```
البضاعة الأولى: 50 كغ × 1.2 د.ت = 60.00 د.ت
البضاعة الثانية: 30 كغ × 1.8 د.ت = 54.00 د.ت
البضاعة الثالثة: 40 كغ × 1.0 د.ت = 40.00 د.ت

المبلغ الخام = 60.00 + 54.00 + 40.00 = 154.00 د.ت
```

### 2. حساب الخصومات

**خصم 4%**:
```
خصم 4% = المبلغ الخام × 0.04
```

**خصم 7%**:
```
خصم 7% = المبلغ الخام × 0.07
```

**مثال**:
```
المبلغ الخام = 154.00 د.ت

خصم 4% = 154.00 × 0.04 = 6.16 د.ت
خصم 7% = 154.00 × 0.07 = 10.78 د.ت
مجموع الخصومات = 6.16 + 10.78 = 16.94 د.ت
```

### 3. حساب الحمولة

**المعادلة العادية**:
```
الحمولة = مجموع (عدد الصناديق × معدل الحمولة) لكل نوع صندوق
```

**معدلات الحمولة** (نفس معدلات الرهن):
| نوع الصندوق | معدل الحمولة (د.ت) |
|-------------|-------------------|
| الصندوق الكبير | 0.200 |
| Plato | 0.200 |
| Lam plus | 0.170 |
| 4 Carro | 0.170 |
| Scarface | 0.170 |
| Lam demi | 0.170 |
| Lam mini | 0.170 |
| Carton | 0.300 |

**حالة "بلا حمولة"**:
```
الحمولة = الوزن الصافي × 0.01
```

**مثال مختلط**:
```
10 صناديق كبيرة: 10 × 0.200 = 2.00 د.ت
5 صناديق Plato: 5 × 0.200 = 1.00 د.ت
20 كغ بلا حمولة: 20 × 0.01 = 0.20 د.ت

مجموع الحمولة = 2.00 + 1.00 + 0.20 = 3.20 د.ت
```

### 4. حساب المبلغ الصافي النهائي

**المعادلة الشاملة**:
```
المبلغ الصافي = المبلغ الخام - خصم 4% - خصم 7% - الحمولة
```

**مثال كامل لفاتورة مورد**:
```
المبلغ الخام = 1000.00 د.ت
خصم 4% = 1000.00 × 0.04 = 40.00 د.ت
خصم 7% = 1000.00 × 0.07 = 70.00 د.ت
الحمولة = 25.00 د.ت

المبلغ الصافي = 1000.00 - 40.00 - 70.00 - 25.00 = 865.00 د.ت
```

## 📊 حسابات الإحصائيات والتقارير

### 1. إحصائيات الحرفاء

**إجمالي ديون الحريف**:
```
إجمالي الديون = مجموع المبالغ غير المدفوعة
```

**إجمالي صناديق الحريف**:
```
إجمالي الصناديق = مجموع الصناديق في جميع العمليات
```

**إجمالي رهون الحريف**:
```
إجمالي الرهون = مجموع الرهون المحجوزة
```

### 2. إحصائيات الموردين

**عدد الصناديق المباعة**:
```
الصناديق المباعة = مجموع الصناديق في جميع عمليات المورد
```

**الوزن الصافي الإجمالي**:
```
الوزن الإجمالي = مجموع الأوزان الصافية لجميع العمليات
```

**متوسط سعر البيع**:
```
متوسط السعر = مجموع (الوزن × السعر) ÷ مجموع الأوزان
```

### 3. إحصائيات البضائع

**المخزون الحالي**:
```
المخزون = الكمية الواردة - الكمية المباعة
```

**معدل الدوران**:
```
معدل الدوران = الكمية المباعة ÷ متوسط المخزون
```

## 🔄 التحديثات التلقائية

### عند تسجيل عملية شراء:

1. **تحديث بيانات الحريف**:
   ```
   الديون الجديدة = الديون السابقة + المبلغ (إذا لم يُدفع)
   الصناديق الجديدة = الصناديق السابقة + عدد الصناديق
   الرهون الجديدة = الرهون السابقة + قيمة الرهن
   ```

2. **تحديث بيانات المورد**:
   ```
   الصناديق المباعة = الصناديق السابقة + عدد الصناديق الجديدة
   الوزن الإجمالي = الوزن السابق + الوزن الصافي الجديد
   ```

3. **تحديث مخزون البضاعة**:
   ```
   المخزون الجديد = المخزون السابق + الوزن الصافي
   ```

### عند تعديل فاتورة مورد:

1. **حساب الفروقات**:
   ```
   فرق الوزن = الوزن الجديد - الوزن القديم
   فرق الصناديق = العدد الجديد - العدد القديم
   ```

2. **تطبيق التغييرات**:
   ```
   بيانات المورد الجديدة = البيانات السابقة + الفروقات
   المخزون الجديد = المخزون السابق + فرق الوزن
   ```

## 🧪 أمثلة تطبيقية شاملة

### مثال 1: عملية شراء كاملة

**البيانات**:
- الحريف: أحمد محمد
- المورد: مورد الخضر
- البضاعة: طماطم
- الوزن القائم: 150 كغ
- عدد الصناديق: 15
- نوع الصندوق: Plato
- سعر الكيلو: 1.3 د.ت

**الحسابات**:
```
الوزن الصافي = 150 - (15 × 1.5) = 150 - 22.5 = 127.5 كغ
المبلغ الجملي = 127.5 × 1.3 = 165.75 د.ت
الرهن = 15 × 0.200 = 3.00 د.ت
المجموع النهائي = 165.75 + 3.00 = 168.75 د.ت
```

### مثال 2: فاتورة مورد متعددة البضائع

**البيانات**:
- المورد: أحمد الخضر
- التاريخ: 2025-01-15

**البضائع**:
1. طماطم: 80 كغ × 1.2 د.ت = 96.00 د.ت
2. خيار: 60 كغ × 1.5 د.ت = 90.00 د.ت
3. بطاطا: 100 كغ × 0.8 د.ت = 80.00 د.ت

**الحسابات**:
```
المبلغ الخام = 96.00 + 90.00 + 80.00 = 266.00 د.ت
خصم 4% = 266.00 × 0.04 = 10.64 د.ت
خصم 7% = 266.00 × 0.07 = 18.62 د.ت
الحمولة = 20 × 0.200 = 4.00 د.ت (20 صندوق كبير)
المبلغ الصافي = 266.00 - 10.64 - 18.62 - 4.00 = 232.74 د.ت
```

---

**ملاحظة**: جميع الحسابات تتم تلقائياً في النظام، وهذا الدليل للمرجعية والفهم العميق للعمليات.
