:root {
    --primary-color: #2e7d32;
    --secondary-color: #ff9800;
    --accent-color: #4caf50;
    --text-color: #333;
    --light-bg: #f5f5f5;
    --card-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--light-bg);
    color: var(--text-color);
    line-height: 1.6;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
}

header h1 {
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 10px;
}

header h2 {
    color: var(--secondary-color);
    font-size: 1.8rem;
}

#dashboard {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    flex: 1;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    text-align: center;
}

.card h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.card p {
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-color);
}

.tabs {
    display: flex;
    background: white;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    margin-bottom: 0;
}

.tab-btn {
    flex: 1;
    padding: 15px;
    background: #e0e0e0;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    transition: background 0.3s;
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    font-weight: bold;
}

.tab-content {
    background: white;
    padding: 30px;
    border-radius: 0 0 8px 8px;
    box-shadow: var(--card-shadow);
    min-height: 400px;
}

footer {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
    #dashboard {
        flex-direction: column;
    }
    
    .tabs {
        flex-direction: column;
    }
}