# خطة التطبيق العملي - سوق الجملة للخضر والغلال

## نظرة عامة على التطبيق

هذا المستند يوضح الخطة العملية لتطبيق النظام المتكامل لإدارة سوق الجملة، مع التركيز على التنفيذ التدريجي والاختبار الشامل.

## 🎯 مراحل التطبيق

### المرحلة الأولى: الأساسيات (الأسبوع 1-2)
- ✅ إنشاء الهيكل الأساسي للنظام
- ✅ تطوير Dashboard الرئيسي
- ✅ إنشاء قاعدة البيانات المحلية
- ✅ تطبيق نظام الحفظ التلقائي

### المرحلة الثانية: إدارة البيانات (الأسبوع 3-4)
- 🔄 تطوير قائمة الحرفاء
- 🔄 تطوير قائمة الموردين
- 🔄 إنشاء قائمة البضائع
- 🔄 تطبيق قائمة الصناديق

### المرحلة الثالثة: العمليات الأساسية (الأسبوع 5-6)
- ⏳ تطوير نظام المشتريات
- ⏳ تطبيق الحسابات التلقائية
- ⏳ ربط الأقسام ببعضها البعض

### المرحلة الرابعة: الفواتير والتقارير (الأسبوع 7-8)
- ⏳ تطوير نظام فواتير الموردين
- ⏳ إنشاء نظام التقارير
- ⏳ تطبيق نظام الطباعة

### المرحلة الخامسة: التحسينات والاختبار (الأسبوع 9-10)
- ⏳ اختبار شامل للنظام
- ⏳ تحسين الأداء
- ⏳ إضافة المميزات المتقدمة

## 🏗️ الهيكل التقني المقترح

### بنية الملفات
```
سوق_الجملة/
├── index.html                 # الصفحة الرئيسية
├── css/
│   ├── main.css              # الأنماط الرئيسية
│   ├── dashboard.css         # أنماط لوحة المعلومات
│   ├── forms.css             # أنماط النماذج
│   └── responsive.css        # التصميم المتجاوب
├── js/
│   ├── app.js                # التطبيق الرئيسي
│   ├── data.js               # إدارة البيانات
│   ├── calculations.js       # الحسابات التلقائية
│   ├── ui.js                 # واجهة المستخدم
│   ├── storage.js            # إدارة التخزين
│   └── utils.js              # الوظائف المساعدة
├── components/
│   ├── dashboard.js          # مكون لوحة المعلومات
│   ├── customers.js          # مكون الحرفاء
│   ├── suppliers.js          # مكون الموردين
│   ├── purchases.js          # مكون المشتريات
│   ├── products.js           # مكون البضائع
│   ├── boxes.js              # مكون الصناديق
│   └── invoices.js           # مكون الفواتير
└── assets/
    ├── icons/                # الأيقونات
    ├── images/               # الصور
    └── fonts/                # الخطوط العربية
```

### قاعدة البيانات المحلية
```javascript
// هيكل البيانات الأساسي
const DATABASE_SCHEMA = {
    customers: {
        id: 'number',
        name: 'string',
        phone: 'string',
        address: 'string',
        debts: 'number',
        boxes: 'number',
        deposits: 'number',
        status: 'string',
        createdAt: 'date',
        updatedAt: 'date'
    },
    
    suppliers: {
        id: 'number',
        name: 'string',
        phone: 'string',
        address: 'string',
        totalBoxesSold: 'number',
        totalWeight: 'number',
        averagePrice: 'number',
        createdAt: 'date',
        updatedAt: 'date'
    },
    
    products: {
        id: 'number',
        name: 'string',
        category: 'string',
        currentStock: 'number',
        minStock: 'number',
        defaultPrice: 'number',
        unit: 'string',
        createdAt: 'date',
        updatedAt: 'date'
    },
    
    boxes: {
        id: 'number',
        name: 'string',
        emptyWeight: 'number',
        loadPrice: 'number',
        totalUsed: 'number',
        totalDeposits: 'number',
        createdAt: 'date',
        updatedAt: 'date'
    },
    
    purchases: {
        id: 'number',
        customerId: 'number',
        supplierId: 'number',
        productType: 'string',
        pricePerKg: 'number',
        boxCount: 'number',
        boxType: 'string',
        grossWeight: 'number',
        netWeight: 'number',
        totalAmount: 'number',
        deposit: 'number',
        paymentStatus: 'string',
        date: 'date',
        createdAt: 'date',
        updatedAt: 'date'
    },
    
    invoices: {
        id: 'number',
        supplierId: 'number',
        date: 'date',
        items: 'array',
        grossAmount: 'number',
        fourPercent: 'number',
        sevenPercent: 'number',
        loadCost: 'number',
        netAmount: 'number',
        status: 'string',
        createdAt: 'date',
        updatedAt: 'date'
    }
};
```

## 🎨 التصميم والواجهة

### نظام الألوان المحدث
```css
:root {
    /* الألوان الأساسية */
    --primary-green: #2e7d32;      /* أخضر السوق */
    --secondary-orange: #ff9800;    /* برتقالي الغلال */
    --accent-lime: #8bc34a;         /* أخضر فاتح */
    --warning-red: #f44336;         /* أحمر التحذير */
    --info-blue: #2196f3;           /* أزرق المعلومات */
    
    /* الألوان المساعدة */
    --text-dark: #212121;
    --text-medium: #424242;
    --text-light: #757575;
    --background-light: #fafafa;
    --background-white: #ffffff;
    --border-light: #e0e0e0;
    
    /* الظلال */
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 25px rgba(0,0,0,0.15);
}
```

### تخطيط الصفحة الرئيسية
```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سوق الجملة للخضر والغلال - جرزونة</title>
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <!-- الرأس -->
    <header class="main-header">
        <div class="header-content">
            <h1>سوق الجملة للخضر والغلال</h1>
            <p>جرزونة - نقطة البيع #14 - بيه الغالي</p>
        </div>
        <div class="header-actions">
            <button class="btn-settings">⚙️ الإعدادات</button>
            <button class="btn-backup">💾 نسخ احتياطي</button>
        </div>
    </header>

    <!-- القائمة الجانبية -->
    <aside class="sidebar">
        <nav class="sidebar-nav">
            <a href="#dashboard" class="nav-item active">
                📊 لوحة المعلومات
            </a>
            <a href="#customers" class="nav-item">
                👥 قائمة الحرفاء
            </a>
            <a href="#purchases" class="nav-item">
                🛒 قائمة المشتريات
            </a>
            <a href="#suppliers" class="nav-item">
                🏭 قائمة الموردين
            </a>
            <a href="#products" class="nav-item">
                📦 قائمة البضائع
            </a>
            <a href="#boxes" class="nav-item">
                📋 قائمة الصناديق
            </a>
            <a href="#invoices" class="nav-item">
                🧾 فواتير الموردين
            </a>
        </nav>
    </aside>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div id="dashboard" class="section active">
            <!-- محتوى لوحة المعلومات -->
        </div>
        
        <div id="customers" class="section">
            <!-- محتوى قائمة الحرفاء -->
        </div>
        
        <!-- باقي الأقسام -->
    </main>

    <!-- التذييل -->
    <footer class="main-footer">
        <p>تطوير: أسامة الصولي | 2025</p>
    </footer>

    <!-- النصوص البرمجية -->
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/calculations.js"></script>
    <script src="js/data.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
```

## 🔧 الوظائف الأساسية

### إدارة التخزين المحلي
```javascript
// storage.js
class LocalStorage {
    static save(key, data) {
        try {
            const serialized = JSON.stringify({
                data: data,
                timestamp: new Date().toISOString(),
                version: '1.0'
            });
            localStorage.setItem(`market_${key}`, serialized);
            return true;
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
            return false;
        }
    }
    
    static load(key) {
        try {
            const stored = localStorage.getItem(`market_${key}`);
            if (!stored) return null;
            
            const parsed = JSON.parse(stored);
            return parsed.data;
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            return null;
        }
    }
    
    static remove(key) {
        localStorage.removeItem(`market_${key}`);
    }
    
    static clear() {
        Object.keys(localStorage)
            .filter(key => key.startsWith('market_'))
            .forEach(key => localStorage.removeItem(key));
    }
}
```

### نظام التحقق من صحة البيانات
```javascript
// utils.js
class Validator {
    static validateCustomer(customer) {
        const errors = [];
        
        if (!customer.name || customer.name.trim().length < 2) {
            errors.push('اسم الحريف مطلوب (حد أدنى حرفان)');
        }
        
        if (customer.phone && !/^\d{8}$/.test(customer.phone)) {
            errors.push('رقم الهاتف يجب أن يكون 8 أرقام');
        }
        
        if (customer.debts < 0) {
            errors.push('الديون لا يمكن أن تكون سالبة');
        }
        
        return errors;
    }
    
    static validatePurchase(purchase) {
        const errors = [];
        
        if (!purchase.customerId) {
            errors.push('يجب اختيار الحريف');
        }
        
        if (!purchase.supplierId) {
            errors.push('يجب اختيار المورد');
        }
        
        if (!purchase.productType) {
            errors.push('يجب اختيار نوع البضاعة');
        }
        
        if (purchase.pricePerKg <= 0) {
            errors.push('سعر الكيلو يجب أن يكون أكبر من صفر');
        }
        
        if (purchase.boxCount <= 0) {
            errors.push('عدد الصناديق يجب أن يكون أكبر من صفر');
        }
        
        if (purchase.grossWeight <= 0) {
            errors.push('الوزن القائم يجب أن يكون أكبر من صفر');
        }
        
        return errors;
    }
}
```

## 📱 التصميم المتجاوب

### نقاط التوقف للشاشات
```css
/* الشاشات الكبيرة - أجهزة الكمبيوتر */
@media (min-width: 1200px) {
    .main-content {
        margin-right: 280px;
    }
    
    .sidebar {
        width: 280px;
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* الشاشات المتوسطة - الأجهزة اللوحية */
@media (max-width: 1199px) and (min-width: 768px) {
    .sidebar {
        width: 240px;
    }
    
    .main-content {
        margin-right: 240px;
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* الشاشات الصغيرة - الهواتف */
@media (max-width: 767px) {
    .sidebar {
        transform: translateX(100%);
        position: fixed;
        z-index: 1000;
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
}
```

## 🧪 خطة الاختبار

### اختبارات الوحدة
- ✅ اختبار الحسابات التلقائية
- ✅ اختبار التحقق من صحة البيانات
- 🔄 اختبار عمليات التخزين
- ⏳ اختبار الترابط بين الأقسام

### اختبارات التكامل
- ⏳ اختبار تدفق البيانات بين الأقسام
- ⏳ اختبار الحفظ والاستعادة
- ⏳ اختبار الأداء مع بيانات كبيرة

### اختبارات المستخدم
- ⏳ اختبار سهولة الاستخدام
- ⏳ اختبار التصميم المتجاوب
- ⏳ اختبار الطباعة والتقارير

## 📈 مؤشرات الأداء

### الأهداف المطلوبة
- ⚡ زمن تحميل الصفحة: أقل من 2 ثانية
- 🔄 زمن الاستجابة للعمليات: أقل من 500 مللي ثانية
- 💾 حجم البيانات المحلية: أقل من 50 ميجابايت
- 📱 دعم الشاشات: من 320px إلى 1920px

### أدوات المراقبة
```javascript
// مراقبة الأداء
class PerformanceMonitor {
    static measureOperation(name, operation) {
        const start = performance.now();
        const result = operation();
        const end = performance.now();
        
        console.log(`${name}: ${(end - start).toFixed(2)}ms`);
        return result;
    }
    
    static measureMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }
}
```

---

**الخطوة التالية**: بدء تطبيق المرحلة الثانية من التطوير مع التركيز على إدارة البيانات والواجهات التفاعلية.
