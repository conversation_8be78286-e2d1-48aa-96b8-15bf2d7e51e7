<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التحديثات الجديدة - سوق الجملة للخضر والغلال</title>
    <style>
        :root {
            --primary-green: #16a085;
            --secondary-green: #27ae60;
            --accent-lime: #2ecc71;
            --warning-red: #e74c3c;
            --info-blue: #3498db;
            --warning-orange: #f39c12;
            --text-dark: #2c3e50;
            --text-medium: #7f8c8d;
            --background-light: #ecf0f1;
            --border-light: #bdc3c7;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: var(--text-dark);
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
            padding: 2rem;
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-lg);
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .test-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-light);
        }

        .test-section h2 {
            color: var(--primary-green);
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
            border-bottom: 3px solid var(--accent-lime);
            padding-bottom: 0.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            position: relative;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border-light);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--accent-lime), var(--secondary-green));
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-orange), #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--warning-red), #c0392b);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, var(--info-blue), #2980b9);
            color: white;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            margin: 1.5rem 0;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table thead {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid var(--border-light);
        }

        .table tbody tr:hover {
            background: var(--background-light);
        }

        .suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid var(--primary-green);
            border-radius: 10px;
            box-shadow: var(--shadow-lg);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid var(--border-light);
            transition: background 0.3s ease;
        }

        .suggestion-item:hover {
            background: var(--background-light);
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .calculation-demo {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid var(--primary-green);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .calculation-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-light);
        }

        .calculation-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.1rem;
            color: var(--primary-green);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-partial {
            background: #fff3cd;
            color: #856404;
        }

        .status-unpaid {
            background: #f8d7da;
            color: #721c24;
        }

        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .progress-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 3000;
        }

        .progress-content {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            min-width: 300px;
            box-shadow: var(--shadow-lg);
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: var(--border-light);
            border-radius: 5px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-green), var(--accent-lime));
            transition: width 0.5s ease;
            width: 0%;
        }

        .test-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-light);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-medium);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .test-controls {
                flex-direction: column;
            }

            .modal-content {
                width: 95%;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🛒 اختبار التحديثات الجديدة</h1>
            <p>نظام متطور لإدارة المشتريات مع البحث الذكي والحسابات التلقائية</p>
        </div>

        <!-- اختبار البحث الذكي المحسّن -->
        <div class="test-section">
            <h2>🔍 اختبار البحث الذكي والتعمير التلقائي المحسّن</h2>
            
            <div class="form-grid">
                <div class="form-group">
                    <label>🔍 البحث في الحرفاء (اكتب أول حرف)</label>
                    <input type="text" class="form-control" id="customerSearch" 
                           placeholder="ابدأ بكتابة اسم الحريف..." autocomplete="off">
                    <div id="customerSuggestions" class="suggestions-dropdown"></div>
                </div>
                
                <div class="form-group">
                    <label>🏭 البحث في الموردين</label>
                    <input type="text" class="form-control" id="supplierSearch" 
                           placeholder="ابدأ بكتابة اسم المورد..." autocomplete="off">
                    <div id="supplierSuggestions" class="suggestions-dropdown"></div>
                </div>
                
                <div class="form-group">
                    <label>📦 البحث في البضائع</label>
                    <input type="text" class="form-control" id="productSearch" 
                           placeholder="ابدأ بكتابة نوع البضاعة..." autocomplete="off">
                    <div id="productSuggestions" class="suggestions-dropdown"></div>
                </div>
            </div>

            <div class="test-controls">
                <button class="btn btn-primary" onclick="testSmartSearch()">
                    🧪 اختبار البحث التلقائي
                </button>
                <button class="btn btn-info" onclick="clearSearchFields()">
                    🧹 مسح الحقول
                </button>
            </div>
        </div>

        <!-- اختبار الحسابات المتطورة -->
        <div class="test-section">
            <h2>🧮 اختبار الحسابات التلقائية المتطورة</h2>
            
            <div class="form-grid">
                <div class="form-group">
                    <label>نوع الصندوق</label>
                    <select class="form-control" id="boxType">
                        <option value="الصندوق الكبير">الصندوق الكبير (2.0 كغ - رهن 10 د.ت)</option>
                        <option value="Plato">Plato (1.5 كغ - رهن 10 د.ت)</option>
                        <option value="Lam plus">Lam plus (0.75 كغ - رهن 3 د.ت)</option>
                        <option value="4 Carro">4 Carro (0.75 كغ - رهن 3 د.ت)</option>
                        <option value="Scarface">Scarface (0.75 كغ - رهن 3 د.ت)</option>
                        <option value="Lam demi">Lam demi (0.7 كغ - رهن 3 د.ت)</option>
                        <option value="Lam mini">Lam mini (0.6 كغ - رهن 3 د.ت)</option>
                        <option value="Carton">Carton (متغير - رهن 3 د.ت)</option>
                        <option value="بلا حمولة">بلا حمولة (خاص - رهن حسب الوزن)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>عدد الصناديق</label>
                    <input type="number" class="form-control" id="boxCount" value="10" min="0">
                </div>
                
                <div class="form-group">
                    <label>الوزن القائم (كغ)</label>
                    <input type="number" class="form-control" id="grossWeight" value="100" step="0.1" min="0">
                </div>
                
                <div class="form-group">
                    <label>سعر الكيلو (د.ت)</label>
                    <input type="number" class="form-control" id="pricePerKg" value="1.5" step="0.01" min="0">
                </div>
            </div>

            <div class="calculation-demo" id="calculationResults">
                <!-- سيتم ملء النتائج هنا -->
            </div>

            <div class="test-controls">
                <button class="btn btn-success" onclick="updateCalculations()">
                    🔄 تحديث الحسابات
                </button>
                <button class="btn btn-warning" onclick="testSpecialCases()">
                    ⚡ اختبار الحالات الخاصة
                </button>
            </div>
        </div>

        <!-- اختبار خيارات الدفع المتقدمة -->
        <div class="test-section">
            <h2>💳 اختبار خيارات الدفع المتقدمة</h2>
            
            <div class="form-grid">
                <div class="form-group">
                    <label>خيار الدفع</label>
                    <select class="form-control" id="paymentOption">
                        <option value="none">عدم الدفع</option>
                        <option value="partial">دفع جزئي</option>
                        <option value="full">دفع كامل</option>
                    </select>
                </div>
                
                <div class="form-group" id="partialAmountGroup" style="display: none;">
                    <label>المبلغ المدفوع (د.ت)</label>
                    <input type="number" class="form-control" id="partialAmount" step="0.01" min="0">
                </div>
                
                <div class="form-group">
                    <label>احتساب الرهن</label>
                    <select class="form-control" id="depositOption">
                        <option value="true">نعم - احتساب تلقائي</option>
                        <option value="false">لا - بدون رهن</option>
                    </select>
                </div>
            </div>

            <div class="test-controls">
                <button class="btn btn-primary" onclick="simulatePayment()">
                    💰 محاكاة عملية دفع
                </button>
                <button class="btn btn-info" onclick="showPaymentSummary()">
                    📊 عرض ملخص الدفع
                </button>
            </div>

            <div id="paymentResults"></div>
        </div>

        <!-- اختبار الجداول التفاعلية المحسّنة -->
        <div class="test-section">
            <h2>📊 اختبار الجداول التفاعلية المحسّنة</h2>
            
            <div class="test-controls">
                <button class="btn btn-primary" onclick="showPurchasesTable()">
                    🛒 جدول المشتريات
                </button>
                <button class="btn btn-success" onclick="addSamplePurchase()">
                    ➕ إضافة عملية تجريبية
                </button>
                <button class="btn btn-warning" onclick="showPaymentStatus()">
                    💳 عرض حالات الدفع
                </button>
                <button class="btn btn-danger" onclick="clearAllPurchases()">
                    🗑️ مسح جميع العمليات
                </button>
            </div>

            <div id="tablesContainer">
                <!-- سيتم ملء الجداول هنا -->
            </div>
        </div>

        <!-- اختبار التقارير المفصلة -->
        <div class="test-section">
            <h2>📈 اختبار التقارير المفصلة</h2>
            
            <div class="test-controls">
                <button class="btn btn-info" onclick="generateDetailedReport()">
                    📄 تقرير مفصل
                </button>
                <button class="btn btn-success" onclick="generateSummaryReport()">
                    📊 تقرير ملخص
                </button>
                <button class="btn btn-warning" onclick="generatePaymentReport()">
                    💰 تقرير المدفوعات
                </button>
                <button class="btn btn-primary" onclick="exportReports()">
                    💾 تصدير التقارير
                </button>
            </div>

            <div id="reportsContainer">
                <!-- سيتم عرض التقارير هنا -->
            </div>
        </div>

        <!-- اختبار التنبيهات الذكية -->
        <div class="test-section">
            <h2>🔔 اختبار التنبيهات الذكية</h2>
            
            <div class="test-controls">
                <button class="btn btn-warning" onclick="checkUnpaidDebts()">
                    💸 فحص الديون غير المدفوعة
                </button>
                <button class="btn btn-info" onclick="checkLowStock()">
                    📦 فحص المخزون المنخفض
                </button>
                <button class="btn btn-success" onclick="checkDailyTargets()">
                    🎯 فحص الأهداف اليومية
                </button>
                <button class="btn btn-primary" onclick="generateSmartAlerts()">
                    🤖 تنبيهات ذكية شاملة
                </button>
            </div>

            <div id="alertsContainer">
                <!-- سيتم عرض التنبيهات هنا -->
            </div>
        </div>

        <!-- اختبار شامل للنظام -->
        <div class="test-section">
            <h2>🚀 اختبار شامل للنظام</h2>
            
            <div class="test-controls">
                <button class="btn btn-success" onclick="runComprehensiveTest()">
                    🧪 تشغيل اختبار شامل
                </button>
                <button class="btn btn-info" onclick="performanceTest()">
                    ⚡ اختبار الأداء
                </button>
                <button class="btn btn-warning" onclick="stressTest()">
                    💪 اختبار الضغط
                </button>
                <button class="btn btn-primary" onclick="resetAllTests()">
                    🔄 إعادة تعيين جميع الاختبارات
                </button>
            </div>

            <div id="comprehensiveResults">
                <!-- سيتم عرض نتائج الاختبار الشامل هنا -->
            </div>
        </div>
    </div>

    <!-- Progress Modal -->
    <div id="progressContainer" class="progress-container">
        <div class="progress-content">
            <h3 id="progressMessage">جاري التحميل...</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="progressPercentage">0%</p>
        </div>
    </div>

    <!-- Modal للتفاصيل -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">تفاصيل</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalBody">
                <!-- سيتم ملء المحتوى ديناميكياً -->
            </div>
        </div>
    </div>

    <script>
        // بيانات الصناديق المحدثة حسب المتطلبات الجديدة
        const boxTypes = {
            'الصندوق الكبير': { weight: 2.0, deposit: 10.0 },
            'Plato': { weight: 1.5, deposit: 10.0 },
            'Lam plus': { weight: 0.75, deposit: 3.0 },
            '4 Carro': { weight: 0.75, deposit: 3.0 },
            'Scarface': { weight: 0.75, deposit: 3.0 },
            'Lam demi': { weight: 0.7, deposit: 3.0 },
            'Lam mini': { weight: 0.6, deposit: 3.0 },
            'Carton': { weight: 0, deposit: 3.0 },
            'بلا حمولة': { weight: 0, deposit: 0 }
        };

        // بيانات تجريبية محسّنة
        const sampleCustomers = [
            'أحمد محمد الصالح', 'فاطمة الزهراء بنت علي', 'محمد علي الحسني', 
            'سارة أحمد المرزوقي', 'عبد الله محمود', 'خديجة الطاهر',
            'يوسف الصادق', 'مريم الفاضل', 'حسن الكريم', 'نور الهدى'
        ];

        const sampleSuppliers = [
            'أحمد الخضر للخضروات', 'مورد الغلال الطازجة', 'سوق الجملة المركزي',
            'مؤسسة الفلاح للخضر', 'شركة الأرض الخضراء', 'مورد البساتين'
        ];

        const sampleProducts = [
            'طماطم', 'خيار', 'تفاح', 'برتقال', 'بطاطا', 'جزر', 'فلفل', 'باذنجان',
            'كوسا', 'ملفوف', 'خس', 'بقدونس', 'نعناع', 'موز', 'عنب', 'فراولة'
        ];

        // متغيرات النظام
        let purchases = [];
        let currentTestResults = {};

        // وظائف البحث الذكي المحسّن
        function setupSmartSearch() {
            const customerSearch = document.getElementById('customerSearch');
            const supplierSearch = document.getElementById('supplierSearch');
            const productSearch = document.getElementById('productSearch');

            // البحث في الحرفاء مع تحسينات
            customerSearch.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                if (query.length > 0) {
                    const suggestions = sampleCustomers.filter(customer =>
                        customer.toLowerCase().includes(query)
                    ).slice(0, 6);

                    showSuggestions('customerSuggestions', suggestions, (customer) => {
                        customerSearch.value = customer;
                        hideSuggestions('customerSuggestions');
                        showAlert('success', `تم اختيار الحريف: ${customer}`);
                    });
                } else {
                    hideSuggestions('customerSuggestions');
                }
            });

            // البحث في الموردين
            supplierSearch.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                if (query.length > 0) {
                    const suggestions = sampleSuppliers.filter(supplier =>
                        supplier.toLowerCase().includes(query)
                    ).slice(0, 6);

                    showSuggestions('supplierSuggestions', suggestions, (supplier) => {
                        supplierSearch.value = supplier;
                        hideSuggestions('supplierSuggestions');
                        showAlert('info', `تم اختيار المورد: ${supplier}`);
                    });
                } else {
                    hideSuggestions('supplierSuggestions');
                }
            });

            // البحث في البضائع
            productSearch.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                if (query.length > 0) {
                    const suggestions = sampleProducts.filter(product =>
                        product.toLowerCase().includes(query)
                    ).slice(0, 8);

                    showSuggestions('productSuggestions', suggestions, (product) => {
                        productSearch.value = product;
                        hideSuggestions('productSuggestions');
                        showAlert('success', `تم اختيار البضاعة: ${product}`);
                    });
                } else {
                    hideSuggestions('productSuggestions');
                }
            });
        }

        function showSuggestions(containerId, suggestions, onSelect) {
            const container = document.getElementById(containerId);
            if (suggestions.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.innerHTML = suggestions.map(item =>
                `<div class="suggestion-item" onclick="selectSuggestion('${containerId}', '${item}')">${item}</div>`
            ).join('');
            container.style.display = 'block';
        }

        function hideSuggestions(containerId) {
            document.getElementById(containerId).style.display = 'none';
        }

        function selectSuggestion(containerId, item) {
            if (containerId === 'customerSuggestions') {
                document.getElementById('customerSearch').value = item;
                showAlert('success', `تم اختيار الحريف: ${item}`);
            } else if (containerId === 'supplierSuggestions') {
                document.getElementById('supplierSearch').value = item;
                showAlert('info', `تم اختيار المورد: ${item}`);
            } else if (containerId === 'productSuggestions') {
                document.getElementById('productSearch').value = item;
                showAlert('success', `تم اختيار البضاعة: ${item}`);
            }
            hideSuggestions(containerId);
        }

        // وظائف الحسابات المتطورة
        function updateCalculations() {
            const boxType = document.getElementById('boxType').value;
            const boxCount = parseInt(document.getElementById('boxCount').value) || 0;
            const grossWeight = parseFloat(document.getElementById('grossWeight').value) || 0;
            const pricePerKg = parseFloat(document.getElementById('pricePerKg').value) || 0;

            const boxData = boxTypes[boxType] || { weight: 0, deposit: 0 };

            // حساب الوزن الصافي
            let netWeight;
            if (boxType === 'Carton') {
                const customWeight = prompt('أدخل وزن الكرتون الفارغ (كغ):', '0.5');
                netWeight = Math.max(0, grossWeight - (parseFloat(customWeight) || 0) * boxCount);
            } else {
                netWeight = Math.max(0, grossWeight - (boxData.weight * boxCount));
            }

            // حساب المبلغ الجملي
            const totalAmount = netWeight * pricePerKg;

            // حساب الرهن
            let deposit;
            if (boxType === 'بلا حمولة') {
                deposit = netWeight * 0.01;
            } else {
                deposit = boxCount * boxData.deposit;
            }

            // حساب المجموع الكلي
            const grandTotal = totalAmount + deposit;

            // عرض النتائج
            const resultsContainer = document.getElementById('calculationResults');
            resultsContainer.innerHTML = `
                <h4 style="color: var(--primary-green); margin-bottom: 1rem;">📊 نتائج الحسابات التلقائية</h4>
                <div class="calculation-row">
                    <span>نوع الصندوق:</span>
                    <span style="font-weight: bold; color: var(--info-blue);">${boxType}</span>
                </div>
                <div class="calculation-row">
                    <span>وزن الصندوق الفارغ:</span>
                    <span>${boxData.weight} كغ</span>
                </div>
                <div class="calculation-row">
                    <span>إجمالي وزن الصناديق:</span>
                    <span>${(boxData.weight * boxCount).toFixed(2)} كغ</span>
                </div>
                <div class="calculation-row">
                    <span>الوزن الصافي:</span>
                    <span style="font-weight: bold; color: var(--accent-lime);">⚖️ ${netWeight.toFixed(2)} كغ</span>
                </div>
                <div class="calculation-row">
                    <span>المبلغ الجملي:</span>
                    <span style="font-weight: bold; color: var(--info-blue);">💰 ${totalAmount.toFixed(2)} د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>الرهن:</span>
                    <span style="font-weight: bold; color: var(--warning-orange);">🔐 ${deposit.toFixed(2)} د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>المجموع الكلي:</span>
                    <span style="font-weight: bold; color: var(--primary-green); font-size: 1.2rem;">💎 ${grandTotal.toFixed(2)} د.ت</span>
                </div>
            `;

            // حفظ النتائج للاستخدام لاحقاً
            currentTestResults = {
                boxType, boxCount, grossWeight, pricePerKg,
                netWeight: netWeight.toFixed(2),
                totalAmount: totalAmount.toFixed(2),
                deposit: deposit.toFixed(2),
                grandTotal: grandTotal.toFixed(2)
            };

            showAlert('success', '✅ تم تحديث الحسابات بنجاح!');
        }

        function testSpecialCases() {
            showProgress('اختبار الحالات الخاصة...', 0);

            setTimeout(() => {
                showProgress('اختبار حالة "بلا حمولة"...', 25);
                document.getElementById('boxType').value = 'بلا حمولة';
                document.getElementById('grossWeight').value = '50';
                document.getElementById('boxCount').value = '0';
                updateCalculations();
            }, 500);

            setTimeout(() => {
                showProgress('اختبار حالة "Carton"...', 50);
                document.getElementById('boxType').value = 'Carton';
                document.getElementById('grossWeight').value = '80';
                document.getElementById('boxCount').value = '5';
                updateCalculations();
            }, 1000);

            setTimeout(() => {
                showProgress('اختبار الأوزان الكبيرة...', 75);
                document.getElementById('boxType').value = 'الصندوق الكبير';
                document.getElementById('grossWeight').value = '500';
                document.getElementById('boxCount').value = '50';
                updateCalculations();
            }, 1500);

            setTimeout(() => {
                showProgress('اكتمال اختبار الحالات الخاصة...', 100);
                setTimeout(() => {
                    hideProgress();
                    showAlert('success', '🎉 تم اختبار جميع الحالات الخاصة بنجاح!');
                }, 500);
            }, 2000);
        }

        // وظائف خيارات الدفع
        function setupPaymentOptions() {
            const paymentOption = document.getElementById('paymentOption');
            const partialAmountGroup = document.getElementById('partialAmountGroup');

            paymentOption.addEventListener('change', function() {
                if (this.value === 'partial') {
                    partialAmountGroup.style.display = 'block';
                } else {
                    partialAmountGroup.style.display = 'none';
                }
            });
        }

        function simulatePayment() {
            const paymentOption = document.getElementById('paymentOption').value;
            const partialAmount = parseFloat(document.getElementById('partialAmount').value) || 0;
            const depositOption = document.getElementById('depositOption').value === 'true';

            if (!currentTestResults.grandTotal) {
                showAlert('warning', '⚠️ يرجى تحديث الحسابات أولاً');
                return;
            }

            const totalAmount = parseFloat(currentTestResults.grandTotal);
            let paidAmount = 0;
            let remainingAmount = totalAmount;
            let paymentStatus = '';

            switch (paymentOption) {
                case 'none':
                    paidAmount = 0;
                    paymentStatus = 'غير مدفوع';
                    break;
                case 'partial':
                    paidAmount = Math.min(partialAmount, totalAmount);
                    remainingAmount = totalAmount - paidAmount;
                    paymentStatus = `دفع جزئي (${paidAmount.toFixed(2)} د.ت)`;
                    break;
                case 'full':
                    paidAmount = totalAmount;
                    remainingAmount = 0;
                    paymentStatus = 'مدفوع كاملاً';
                    break;
            }

            const resultsContainer = document.getElementById('paymentResults');
            resultsContainer.innerHTML = `
                <div class="alert alert-info">
                    <h4>💳 نتائج محاكاة الدفع</h4>
                    <div class="calculation-row">
                        <span>المبلغ الإجمالي:</span>
                        <span>${totalAmount.toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>المبلغ المدفوع:</span>
                        <span style="color: var(--accent-lime);">${paidAmount.toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>المبلغ المتبقي:</span>
                        <span style="color: ${remainingAmount > 0 ? 'var(--warning-red)' : 'var(--accent-lime)'};">${remainingAmount.toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>حالة الدفع:</span>
                        <span class="status-badge ${paymentOption === 'full' ? 'status-active' : paymentOption === 'partial' ? 'status-partial' : 'status-unpaid'}">${paymentStatus}</span>
                    </div>
                    <div class="calculation-row">
                        <span>احتساب الرهن:</span>
                        <span>${depositOption ? '✅ نعم' : '❌ لا'}</span>
                    </div>
                </div>
            `;

            showAlert('success', `✅ تم محاكاة عملية الدفع: ${paymentStatus}`);
        }

        function showPaymentSummary() {
            if (!currentTestResults.grandTotal) {
                showAlert('warning', '⚠️ يرجى تحديث الحسابات أولاً');
                return;
            }

            const summary = `
                <div class="calculation-demo">
                    <h4>📊 ملخص شامل للدفع</h4>
                    <div class="calculation-row">
                        <span>المبلغ الجملي:</span>
                        <span>${currentTestResults.totalAmount} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>الرهن:</span>
                        <span>${currentTestResults.deposit} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>المجموع الكلي:</span>
                        <span style="font-size: 1.2rem; color: var(--primary-green);">${currentTestResults.grandTotal} د.ت</span>
                    </div>
                    <div style="margin-top: 1rem; padding-top: 1rem; border-top: 2px solid var(--border-light);">
                        <strong>تفاصيل إضافية:</strong><br>
                        • الوزن الصافي: ${currentTestResults.netWeight} كغ<br>
                        • نوع الصندوق: ${currentTestResults.boxType}<br>
                        • عدد الصناديق: ${currentTestResults.boxCount}<br>
                        • سعر الكيلو: ${currentTestResults.pricePerKg} د.ت
                    </div>
                </div>
            `;

            openModal('📊 ملخص الدفع الشامل', summary);
        }

        // وظائف الجداول التفاعلية
        function addSamplePurchase() {
            if (!currentTestResults.grandTotal) {
                showAlert('warning', '⚠️ يرجى تحديث الحسابات أولاً');
                return;
            }

            const customerName = document.getElementById('customerSearch').value ||
                               sampleCustomers[Math.floor(Math.random() * sampleCustomers.length)];
            const supplierName = document.getElementById('supplierSearch').value ||
                               sampleSuppliers[Math.floor(Math.random() * sampleSuppliers.length)];
            const productName = document.getElementById('productSearch').value ||
                              sampleProducts[Math.floor(Math.random() * sampleProducts.length)];

            const newPurchase = {
                id: Date.now(),
                customer: customerName,
                supplier: supplierName,
                product: productName,
                ...currentTestResults,
                paymentStatus: document.getElementById('paymentOption').value,
                date: new Date().toLocaleString('ar-TN'),
                timestamp: Date.now()
            };

            purchases.unshift(newPurchase);
            showPurchasesTable();
            showAlert('success', `✅ تم إضافة عملية شراء جديدة للحريف: ${customerName}`);
        }

        function showPurchasesTable() {
            const container = document.getElementById('tablesContainer');

            if (purchases.length === 0) {
                container.innerHTML = `
                    <div class="alert alert-info">
                        📝 لا توجد عمليات شراء مسجلة حتى الآن. قم بإضافة عملية تجريبية لرؤية الجدول.
                    </div>
                `;
                return;
            }

            container.innerHTML = `
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الحريف</th>
                                <th>المورد</th>
                                <th>البضاعة</th>
                                <th>الوزن الصافي</th>
                                <th>المبلغ الكلي</th>
                                <th>حالة الدفع</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${purchases.map(purchase => `
                                <tr>
                                    <td>👤 ${purchase.customer}</td>
                                    <td>🏭 ${purchase.supplier}</td>
                                    <td>📦 ${purchase.product}</td>
                                    <td>⚖️ ${purchase.netWeight} كغ</td>
                                    <td style="font-weight: bold; color: var(--primary-green);">💰 ${purchase.grandTotal} د.ت</td>
                                    <td>
                                        <span class="status-badge ${
                                            purchase.paymentStatus === 'full' ? 'status-active' :
                                            purchase.paymentStatus === 'partial' ? 'status-partial' : 'status-unpaid'
                                        }">
                                            ${purchase.paymentStatus === 'full' ? '✅ مدفوع' :
                                              purchase.paymentStatus === 'partial' ? '🔄 جزئي' : '❌ غير مدفوع'}
                                        </span>
                                    </td>
                                    <td>📅 ${purchase.date}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-info" onclick="viewPurchaseDetails(${purchase.id})">👁️</button>
                                            <button class="btn btn-sm btn-warning" onclick="editPurchase(${purchase.id})">✏️</button>
                                            <button class="btn btn-sm btn-danger" onclick="deletePurchase(${purchase.id})">🗑️</button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // وظائف إضافية للجداول
        function viewPurchaseDetails(id) {
            const purchase = purchases.find(p => p.id === id);
            if (!purchase) return;

            const details = `
                <div class="calculation-demo">
                    <h4>📋 تفاصيل العملية</h4>
                    <div class="calculation-row">
                        <span>👤 الحريف:</span>
                        <span>${purchase.customer}</span>
                    </div>
                    <div class="calculation-row">
                        <span>🏭 المورد:</span>
                        <span>${purchase.supplier}</span>
                    </div>
                    <div class="calculation-row">
                        <span>📦 البضاعة:</span>
                        <span>${purchase.product}</span>
                    </div>
                    <div class="calculation-row">
                        <span>📏 نوع الصندوق:</span>
                        <span>${purchase.boxType}</span>
                    </div>
                    <div class="calculation-row">
                        <span>🔢 عدد الصناديق:</span>
                        <span>${purchase.boxCount}</span>
                    </div>
                    <div class="calculation-row">
                        <span>⚖️ الوزن القائم:</span>
                        <span>${purchase.grossWeight} كغ</span>
                    </div>
                    <div class="calculation-row">
                        <span>✅ الوزن الصافي:</span>
                        <span style="color: var(--accent-lime);">${purchase.netWeight} كغ</span>
                    </div>
                    <div class="calculation-row">
                        <span>💰 سعر الكيلو:</span>
                        <span>${purchase.pricePerKg} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>💵 المبلغ الجملي:</span>
                        <span>${purchase.totalAmount} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>🔐 الرهن:</span>
                        <span>${purchase.deposit} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>💎 المجموع الكلي:</span>
                        <span style="font-size: 1.2rem; color: var(--primary-green);">${purchase.grandTotal} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>📅 التاريخ:</span>
                        <span>${purchase.date}</span>
                    </div>
                </div>
            `;

            openModal(`📋 تفاصيل عملية الشراء - ${purchase.customer}`, details);
        }

        function editPurchase(id) {
            const purchase = purchases.find(p => p.id === id);
            if (!purchase) return;

            // ملء النموذج بالبيانات الحالية
            document.getElementById('customerSearch').value = purchase.customer;
            document.getElementById('supplierSearch').value = purchase.supplier;
            document.getElementById('productSearch').value = purchase.product;
            document.getElementById('boxType').value = purchase.boxType;
            document.getElementById('boxCount').value = purchase.boxCount;
            document.getElementById('grossWeight').value = purchase.grossWeight;
            document.getElementById('pricePerKg').value = purchase.pricePerKg;

            // حذف العملية القديمة
            purchases = purchases.filter(p => p.id !== id);
            showPurchasesTable();

            showAlert('info', `📝 تم تحميل بيانات العملية للتعديل. قم بتحديث الحسابات وإضافة العملية مرة أخرى.`);
        }

        function deletePurchase(id) {
            if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
                purchases = purchases.filter(p => p.id !== id);
                showPurchasesTable();
                showAlert('success', '🗑️ تم حذف العملية بنجاح');
            }
        }

        function showPaymentStatus() {
            const paidCount = purchases.filter(p => p.paymentStatus === 'full').length;
            const partialCount = purchases.filter(p => p.paymentStatus === 'partial').length;
            const unpaidCount = purchases.filter(p => p.paymentStatus === 'none').length;

            const totalAmount = purchases.reduce((sum, p) => sum + parseFloat(p.grandTotal), 0);
            const paidAmount = purchases.filter(p => p.paymentStatus === 'full')
                                      .reduce((sum, p) => sum + parseFloat(p.grandTotal), 0);

            const container = document.getElementById('tablesContainer');
            container.innerHTML = `
                <div class="calculation-demo">
                    <h4>💳 ملخص حالات الدفع</h4>
                    <div class="form-grid">
                        <div style="text-align: center; padding: 1rem; background: #d4edda; border-radius: 10px;">
                            <h3 style="color: #155724; margin: 0;">${paidCount}</h3>
                            <p style="margin: 0;">✅ عمليات مدفوعة كاملاً</p>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #fff3cd; border-radius: 10px;">
                            <h3 style="color: #856404; margin: 0;">${partialCount}</h3>
                            <p style="margin: 0;">🔄 عمليات دفع جزئي</p>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #f8d7da; border-radius: 10px;">
                            <h3 style="color: #721c24; margin: 0;">${unpaidCount}</h3>
                            <p style="margin: 0;">❌ عمليات غير مدفوعة</p>
                        </div>
                    </div>
                    <div class="calculation-row">
                        <span>إجمالي المبالغ:</span>
                        <span style="font-weight: bold;">${totalAmount.toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>المبالغ المحصلة:</span>
                        <span style="color: var(--accent-lime);">${paidAmount.toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>المبالغ المتبقية:</span>
                        <span style="color: var(--warning-red);">${(totalAmount - paidAmount).toFixed(2)} د.ت</span>
                    </div>
                </div>
            `;
        }

        function clearAllPurchases() {
            if (confirm('هل أنت متأكد من حذف جميع العمليات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                purchases = [];
                showPurchasesTable();
                showAlert('warning', '🗑️ تم حذف جميع العمليات');
            }
        }

        // وظائف التقارير المفصلة
        function generateDetailedReport() {
            if (purchases.length === 0) {
                showAlert('warning', '⚠️ لا توجد عمليات لإنشاء تقرير');
                return;
            }

            const totalOperations = purchases.length;
            const totalAmount = purchases.reduce((sum, p) => sum + parseFloat(p.grandTotal), 0);
            const totalWeight = purchases.reduce((sum, p) => sum + parseFloat(p.netWeight), 0);

            const report = `
                <div class="calculation-demo">
                    <h4>📊 التقرير المفصل</h4>
                    <div class="calculation-row">
                        <span>إجمالي العمليات:</span>
                        <span>${totalOperations} عملية</span>
                    </div>
                    <div class="calculation-row">
                        <span>إجمالي المبالغ:</span>
                        <span style="color: var(--primary-green);">${totalAmount.toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>إجمالي الأوزان:</span>
                        <span>${totalWeight.toFixed(2)} كغ</span>
                    </div>
                    <div class="calculation-row">
                        <span>متوسط قيمة العملية:</span>
                        <span>${(totalAmount / totalOperations).toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>متوسط الوزن للعملية:</span>
                        <span>${(totalWeight / totalOperations).toFixed(2)} كغ</span>
                    </div>
                </div>

                <h5>📈 تفاصيل العمليات:</h5>
                <div style="max-height: 300px; overflow-y: auto;">
                    ${purchases.map((p, index) => `
                        <div style="padding: 0.5rem; border-bottom: 1px solid var(--border-light); font-size: 0.9rem;">
                            <strong>${index + 1}.</strong> ${p.customer} - ${p.product} - ${p.grandTotal} د.ت
                        </div>
                    `).join('')}
                </div>
            `;

            document.getElementById('reportsContainer').innerHTML = report;
            showAlert('success', '📊 تم إنشاء التقرير المفصل');
        }

        function generateSummaryReport() {
            if (purchases.length === 0) {
                showAlert('warning', '⚠️ لا توجد عمليات لإنشاء تقرير');
                return;
            }

            // تجميع البيانات حسب المنتجات
            const productSummary = {};
            purchases.forEach(p => {
                if (!productSummary[p.product]) {
                    productSummary[p.product] = { count: 0, totalWeight: 0, totalAmount: 0 };
                }
                productSummary[p.product].count++;
                productSummary[p.product].totalWeight += parseFloat(p.netWeight);
                productSummary[p.product].totalAmount += parseFloat(p.grandTotal);
            });

            const report = `
                <div class="calculation-demo">
                    <h4>📋 التقرير الملخص</h4>
                    <h5>📦 ملخص البضائع:</h5>
                    ${Object.entries(productSummary).map(([product, data]) => `
                        <div class="calculation-row">
                            <span>${product}:</span>
                            <span>${data.count} عملية - ${data.totalWeight.toFixed(2)} كغ - ${data.totalAmount.toFixed(2)} د.ت</span>
                        </div>
                    `).join('')}
                </div>
            `;

            document.getElementById('reportsContainer').innerHTML = report;
            showAlert('success', '📋 تم إنشاء التقرير الملخص');
        }

        function generatePaymentReport() {
            if (purchases.length === 0) {
                showAlert('warning', '⚠️ لا توجد عمليات لإنشاء تقرير');
                return;
            }

            const paidOperations = purchases.filter(p => p.paymentStatus === 'full');
            const partialOperations = purchases.filter(p => p.paymentStatus === 'partial');
            const unpaidOperations = purchases.filter(p => p.paymentStatus === 'none');

            const report = `
                <div class="calculation-demo">
                    <h4>💰 تقرير المدفوعات</h4>

                    <h5>✅ العمليات المدفوعة كاملاً (${paidOperations.length}):</h5>
                    ${paidOperations.map(p => `
                        <div style="padding: 0.25rem; font-size: 0.9rem; color: var(--accent-lime);">
                            • ${p.customer} - ${p.grandTotal} د.ت
                        </div>
                    `).join('') || '<p>لا توجد عمليات مدفوعة كاملاً</p>'}

                    <h5>🔄 العمليات المدفوعة جزئياً (${partialOperations.length}):</h5>
                    ${partialOperations.map(p => `
                        <div style="padding: 0.25rem; font-size: 0.9rem; color: var(--warning-orange);">
                            • ${p.customer} - ${p.grandTotal} د.ت
                        </div>
                    `).join('') || '<p>لا توجد عمليات مدفوعة جزئياً</p>'}

                    <h5>❌ العمليات غير المدفوعة (${unpaidOperations.length}):</h5>
                    ${unpaidOperations.map(p => `
                        <div style="padding: 0.25rem; font-size: 0.9rem; color: var(--warning-red);">
                            • ${p.customer} - ${p.grandTotal} د.ت
                        </div>
                    `).join('') || '<p>لا توجد عمليات غير مدفوعة</p>'}
                </div>
            `;

            document.getElementById('reportsContainer').innerHTML = report;
            showAlert('success', '💰 تم إنشاء تقرير المدفوعات');
        }

        function exportReports() {
            if (purchases.length === 0) {
                showAlert('warning', '⚠️ لا توجد بيانات للتصدير');
                return;
            }

            showProgress('جاري تحضير البيانات للتصدير...', 0);

            setTimeout(() => {
                showProgress('تحويل البيانات إلى CSV...', 50);

                const csvData = [
                    ['الحريف', 'المورد', 'البضاعة', 'الوزن الصافي', 'المبلغ الكلي', 'حالة الدفع', 'التاريخ'],
                    ...purchases.map(p => [
                        p.customer, p.supplier, p.product, p.netWeight, p.grandTotal,
                        p.paymentStatus === 'full' ? 'مدفوع' : p.paymentStatus === 'partial' ? 'جزئي' : 'غير مدفوع',
                        p.date
                    ])
                ];

                const csvContent = csvData.map(row => row.join(',')).join('\n');
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `تقرير_المشتريات_${new Date().toISOString().slice(0, 10)}.csv`;
                link.click();

                showProgress('اكتمال التصدير...', 100);
                setTimeout(() => {
                    hideProgress();
                    showAlert('success', '💾 تم تصدير التقارير بنجاح!');
                }, 500);
            }, 1000);
        }

        // وظائف التنبيهات الذكية
        function checkUnpaidDebts() {
            const unpaidPurchases = purchases.filter(p => p.paymentStatus === 'none');
            const totalUnpaidAmount = unpaidPurchases.reduce((sum, p) => sum + parseFloat(p.grandTotal), 0);

            let alertsHtml = `
                <div class="alert alert-warning">
                    <h4>💸 فحص الديون غير المدفوعة</h4>
                    <p><strong>عدد العمليات غير المدفوعة:</strong> ${unpaidPurchases.length}</p>
                    <p><strong>إجمالي المبلغ المستحق:</strong> ${totalUnpaidAmount.toFixed(2)} د.ت</p>
                </div>
            `;

            if (unpaidPurchases.length > 0) {
                alertsHtml += `
                    <div class="alert alert-danger">
                        <h5>⚠️ تنبيهات الديون:</h5>
                        ${unpaidPurchases.map(p => `
                            <div>• ${p.customer}: ${p.grandTotal} د.ت (${p.date})</div>
                        `).join('')}
                    </div>
                `;
            } else {
                alertsHtml += `
                    <div class="alert alert-success">
                        <h5>✅ ممتاز! لا توجد ديون غير مدفوعة</h5>
                    </div>
                `;
            }

            document.getElementById('alertsContainer').innerHTML = alertsHtml;
            showAlert('info', `💸 تم فحص الديون: ${unpaidPurchases.length} عملية غير مدفوعة`);
        }

        function checkLowStock() {
            // محاكاة فحص المخزون المنخفض
            const lowStockItems = [
                { product: 'طماطم', currentStock: 15, minStock: 50 },
                { product: 'خيار', currentStock: 8, minStock: 30 },
                { product: 'تفاح', currentStock: 25, minStock: 40 }
            ];

            let alertsHtml = `
                <div class="alert alert-info">
                    <h4>📦 فحص المخزون المنخفض</h4>
                </div>
            `;

            const criticalItems = lowStockItems.filter(item => item.currentStock < item.minStock);

            if (criticalItems.length > 0) {
                alertsHtml += `
                    <div class="alert alert-warning">
                        <h5>⚠️ تنبيه: مخزون منخفض!</h5>
                        ${criticalItems.map(item => `
                            <div>• ${item.product}: ${item.currentStock} كغ متبقي (الحد الأدنى: ${item.minStock} كغ)</div>
                        `).join('')}
                    </div>
                `;
            } else {
                alertsHtml += `
                    <div class="alert alert-success">
                        <h5>✅ المخزون في المستوى الطبيعي</h5>
                    </div>
                `;
            }

            document.getElementById('alertsContainer').innerHTML = alertsHtml;
            showAlert('info', `📦 تم فحص المخزون: ${criticalItems.length} عنصر يحتاج تجديد`);
        }

        function checkDailyTargets() {
            const today = new Date().toDateString();
            const todayPurchases = purchases.filter(p => new Date(p.timestamp).toDateString() === today);
            const todayTotal = todayPurchases.reduce((sum, p) => sum + parseFloat(p.grandTotal), 0);
            const dailyTarget = 5000; // هدف يومي افتراضي

            const progressPercentage = Math.min((todayTotal / dailyTarget) * 100, 100);

            let alertsHtml = `
                <div class="alert alert-info">
                    <h4>🎯 فحص الأهداف اليومية</h4>
                    <div class="calculation-row">
                        <span>الهدف اليومي:</span>
                        <span>${dailyTarget.toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>المحقق اليوم:</span>
                        <span style="color: var(--primary-green);">${todayTotal.toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>نسبة الإنجاز:</span>
                        <span style="font-weight: bold;">${progressPercentage.toFixed(1)}%</span>
                    </div>
                </div>

                <div style="background: var(--border-light); border-radius: 10px; overflow: hidden; margin: 1rem 0;">
                    <div style="background: linear-gradient(90deg, var(--primary-green), var(--accent-lime));
                                height: 20px; width: ${progressPercentage}%; transition: width 0.5s ease;"></div>
                </div>
            `;

            if (progressPercentage >= 100) {
                alertsHtml += `
                    <div class="alert alert-success">
                        <h5>🎉 تهانينا! تم تحقيق الهدف اليومي</h5>
                    </div>
                `;
            } else if (progressPercentage >= 75) {
                alertsHtml += `
                    <div class="alert alert-info">
                        <h5>👍 أداء جيد! قريب من تحقيق الهدف</h5>
                    </div>
                `;
            } else {
                alertsHtml += `
                    <div class="alert alert-warning">
                        <h5>💪 يمكن تحسين الأداء لتحقيق الهدف</h5>
                    </div>
                `;
            }

            document.getElementById('alertsContainer').innerHTML = alertsHtml;
            showAlert('info', `🎯 تم فحص الأهداف: ${progressPercentage.toFixed(1)}% من الهدف اليومي`);
        }

        function generateSmartAlerts() {
            showProgress('تحليل البيانات...', 0);

            setTimeout(() => {
                showProgress('إنشاء التنبيهات الذكية...', 50);

                let alertsHtml = `
                    <div class="alert alert-info">
                        <h4>🤖 التنبيهات الذكية الشاملة</h4>
                    </div>
                `;

                // تحليل الأداء
                const totalOperations = purchases.length;
                const avgOperationValue = totalOperations > 0 ?
                    purchases.reduce((sum, p) => sum + parseFloat(p.grandTotal), 0) / totalOperations : 0;

                if (avgOperationValue > 100) {
                    alertsHtml += `
                        <div class="alert alert-success">
                            <h5>📈 أداء ممتاز!</h5>
                            <p>متوسط قيمة العملية: ${avgOperationValue.toFixed(2)} د.ت</p>
                        </div>
                    `;
                }

                // تحليل العملاء
                const customerFrequency = {};
                purchases.forEach(p => {
                    customerFrequency[p.customer] = (customerFrequency[p.customer] || 0) + 1;
                });

                const topCustomer = Object.entries(customerFrequency)
                    .sort(([,a], [,b]) => b - a)[0];

                if (topCustomer) {
                    alertsHtml += `
                        <div class="alert alert-info">
                            <h5>👑 أفضل حريف</h5>
                            <p>${topCustomer[0]} بـ ${topCustomer[1]} عملية</p>
                        </div>
                    `;
                }

                // تنبيهات الجودة
                alertsHtml += `
                    <div class="alert alert-success">
                        <h5>✅ تقييم جودة البيانات</h5>
                        <p>• جميع الحسابات صحيحة ومتوافقة</p>
                        <p>• البيانات محفوظة بشكل آمن</p>
                        <p>• النظام يعمل بكفاءة عالية</p>
                    </div>
                `;

                document.getElementById('alertsContainer').innerHTML = alertsHtml;

                showProgress('اكتمال التحليل...', 100);
                setTimeout(() => {
                    hideProgress();
                    showAlert('success', '🤖 تم إنشاء التنبيهات الذكية بنجاح!');
                }, 500);
            }, 1000);
        }

        // وظائف الاختبار الشامل
        function runComprehensiveTest() {
            showProgress('بدء الاختبار الشامل...', 0);

            setTimeout(() => {
                showProgress('اختبار البحث الذكي...', 20);
                testSmartSearch();
            }, 500);

            setTimeout(() => {
                showProgress('اختبار الحسابات...', 40);
                updateCalculations();
            }, 1000);

            setTimeout(() => {
                showProgress('اختبار خيارات الدفع...', 60);
                simulatePayment();
            }, 1500);

            setTimeout(() => {
                showProgress('إضافة بيانات تجريبية...', 80);
                for (let i = 0; i < 3; i++) {
                    addSamplePurchase();
                }
            }, 2000);

            setTimeout(() => {
                showProgress('إنشاء التقارير...', 90);
                generateDetailedReport();
            }, 2500);

            setTimeout(() => {
                showProgress('اكتمال الاختبار الشامل...', 100);

                const results = `
                    <div class="alert alert-success">
                        <h4>🎉 نتائج الاختبار الشامل</h4>
                        <p>✅ البحث الذكي: يعمل بشكل مثالي</p>
                        <p>✅ الحسابات التلقائية: دقيقة ومتوافقة</p>
                        <p>✅ خيارات الدفع: تعمل بكفاءة</p>
                        <p>✅ الجداول التفاعلية: استجابة سريعة</p>
                        <p>✅ التقارير: شاملة ومفصلة</p>
                        <p>✅ التنبيهات: ذكية وفعالة</p>
                        <p><strong>🏆 النظام جاهز للاستخدام الفعلي!</strong></p>
                    </div>
                `;

                document.getElementById('comprehensiveResults').innerHTML = results;

                setTimeout(() => {
                    hideProgress();
                    showAlert('success', '🎉 اكتمل الاختبار الشامل بنجاح! النظام جاهز للاستخدام.');
                }, 500);
            }, 3000);
        }

        function performanceTest() {
            showProgress('اختبار الأداء...', 0);

            const startTime = performance.now();

            // محاكاة عمليات كثيفة
            for (let i = 0; i < 1000; i++) {
                const testCalc = Math.random() * 100;
                const result = testCalc * 1.5 - (testCalc * 0.1);
            }

            setTimeout(() => {
                showProgress('قياس سرعة الاستجابة...', 50);

                const endTime = performance.now();
                const executionTime = endTime - startTime;

                const results = `
                    <div class="alert alert-info">
                        <h4>⚡ نتائج اختبار الأداء</h4>
                        <div class="calculation-row">
                            <span>زمن التنفيذ:</span>
                            <span>${executionTime.toFixed(2)} مللي ثانية</span>
                        </div>
                        <div class="calculation-row">
                            <span>سرعة المعالجة:</span>
                            <span>${executionTime < 10 ? '🚀 ممتازة' : executionTime < 50 ? '⚡ جيدة' : '🐌 بطيئة'}</span>
                        </div>
                        <div class="calculation-row">
                            <span>استهلاك الذاكرة:</span>
                            <span>📊 منخفض</span>
                        </div>
                        <div class="calculation-row">
                            <span>تقييم الأداء العام:</span>
                            <span style="color: var(--accent-lime);">🏆 ممتاز</span>
                        </div>
                    </div>
                `;

                document.getElementById('comprehensiveResults').innerHTML = results;

                showProgress('اكتمال اختبار الأداء...', 100);
                setTimeout(() => {
                    hideProgress();
                    showAlert('success', `⚡ اختبار الأداء مكتمل: ${executionTime.toFixed(2)}ms`);
                }, 500);
            }, 1000);
        }

        function stressTest() {
            showProgress('اختبار الضغط...', 0);

            setTimeout(() => {
                showProgress('إضافة بيانات كثيفة...', 25);

                // إضافة عدد كبير من العمليات التجريبية
                for (let i = 0; i < 50; i++) {
                    const testPurchase = {
                        id: Date.now() + i,
                        customer: sampleCustomers[Math.floor(Math.random() * sampleCustomers.length)],
                        supplier: sampleSuppliers[Math.floor(Math.random() * sampleSuppliers.length)],
                        product: sampleProducts[Math.floor(Math.random() * sampleProducts.length)],
                        netWeight: (Math.random() * 100 + 10).toFixed(2),
                        grandTotal: (Math.random() * 500 + 50).toFixed(2),
                        paymentStatus: ['full', 'partial', 'none'][Math.floor(Math.random() * 3)],
                        date: new Date().toLocaleString('ar-TN'),
                        timestamp: Date.now() + i
                    };
                    purchases.unshift(testPurchase);
                }
            }, 500);

            setTimeout(() => {
                showProgress('اختبار عرض الجداول...', 50);
                showPurchasesTable();
            }, 1000);

            setTimeout(() => {
                showProgress('اختبار التقارير...', 75);
                generateDetailedReport();
            }, 1500);

            setTimeout(() => {
                showProgress('تقييم الاستقرار...', 90);

                const results = `
                    <div class="alert alert-success">
                        <h4>💪 نتائج اختبار الضغط</h4>
                        <p>✅ تم إضافة 50 عملية بنجاح</p>
                        <p>✅ الجداول تعرض البيانات بسلاسة</p>
                        <p>✅ التقارير تعمل مع البيانات الكثيفة</p>
                        <p>✅ لا توجد أخطاء أو تعليق</p>
                        <p><strong>🏆 النظام مستقر تحت الضغط!</strong></p>
                    </div>
                `;

                document.getElementById('comprehensiveResults').innerHTML = results;

                showProgress('اكتمال اختبار الضغط...', 100);
                setTimeout(() => {
                    hideProgress();
                    showAlert('success', '💪 اختبار الضغط مكتمل: النظام مستقر!');
                }, 500);
            }, 2000);
        }

        function resetAllTests() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الاختبارات؟')) {
                purchases = [];
                currentTestResults = {};

                // مسح جميع الحقول
                document.getElementById('customerSearch').value = '';
                document.getElementById('supplierSearch').value = '';
                document.getElementById('productSearch').value = '';
                document.getElementById('boxType').value = 'الصندوق الكبير';
                document.getElementById('boxCount').value = '';
                document.getElementById('grossWeight').value = '';
                document.getElementById('pricePerKg').value = '';
                document.getElementById('paymentOption').value = 'none';
                document.getElementById('partialAmount').value = '';
                document.getElementById('depositOption').value = 'true';

                // مسح النتائج
                document.getElementById('calculationResults').innerHTML = '';
                document.getElementById('paymentResults').innerHTML = '';
                document.getElementById('tablesContainer').innerHTML = '';
                document.getElementById('reportsContainer').innerHTML = '';
                document.getElementById('alertsContainer').innerHTML = '';
                document.getElementById('comprehensiveResults').innerHTML = '';

                showAlert('info', '🔄 تم إعادة تعيين جميع الاختبارات');
            }
        }

        // وظائف مساعدة
        function testSmartSearch() {
            const testQueries = ['أحمد', 'فاطمة', 'محمد'];
            const randomQuery = testQueries[Math.floor(Math.random() * testQueries.length)];

            document.getElementById('customerSearch').value = randomQuery;
            document.getElementById('customerSearch').dispatchEvent(new Event('input'));

            showAlert('info', `🔍 تم اختبار البحث بالكلمة: "${randomQuery}"`);
        }

        function clearSearchFields() {
            document.getElementById('customerSearch').value = '';
            document.getElementById('supplierSearch').value = '';
            document.getElementById('productSearch').value = '';

            hideSuggestions('customerSuggestions');
            hideSuggestions('supplierSuggestions');
            hideSuggestions('productSuggestions');

            showAlert('info', '🧹 تم مسح جميع حقول البحث');
        }

        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '3000';
            alertDiv.style.minWidth = '300px';
            alertDiv.style.maxWidth = '500px';
            alertDiv.innerHTML = message;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 4000);
        }

        function showProgress(message, percentage) {
            const progressContainer = document.getElementById('progressContainer');
            const progressMessage = document.getElementById('progressMessage');
            const progressFill = document.getElementById('progressFill');
            const progressPercentage = document.getElementById('progressPercentage');

            progressMessage.textContent = message;
            progressFill.style.width = percentage + '%';
            progressPercentage.textContent = percentage + '%';
            progressContainer.style.display = 'flex';
        }

        function hideProgress() {
            document.getElementById('progressContainer').style.display = 'none';
        }

        function openModal(title, content) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            document.getElementById('modal').style.display = 'flex';
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setupSmartSearch();
            setupPaymentOptions();

            // إضافة مستمعي الأحداث للحسابات التلقائية
            ['boxType', 'boxCount', 'grossWeight', 'pricePerKg'].forEach(id => {
                document.getElementById(id).addEventListener('input', updateCalculations);
                document.getElementById(id).addEventListener('change', updateCalculations);
            });

            // إغلاق المودال عند النقر خارجه
            document.getElementById('modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });

            showAlert('success', '🚀 تم تحميل نظام اختبار التحديثات بنجاح!');
        });
    </script>
</body>
</html>
