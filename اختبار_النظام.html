<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - سوق الجملة للخضر والغلال</title>
    <style>
        :root {
            --primary-green: #2e7d32;
            --secondary-orange: #ff9800;
            --accent-lime: #8bc34a;
            --warning-red: #f44336;
            --info-blue: #2196f3;
            --text-dark: #212121;
            --text-medium: #424242;
            --text-light: #757575;
            --background-light: #fafafa;
            --background-white: #ffffff;
            --border-light: #e0e0e0;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--background-light);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .main-header {
            background: linear-gradient(135deg, var(--primary-green) 0%, #1b5e20 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: var(--shadow-md);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-title h1 {
            font-size: 1.8rem;
            margin-bottom: 0.2rem;
        }

        .header-title p {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--secondary-orange);
            color: white;
        }

        .btn-primary:hover {
            background: #f57c00;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
        }

        .test-section h2 {
            color: var(--primary-green);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            border-bottom: 2px solid var(--accent-lime);
            padding-bottom: 0.5rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--accent-lime) 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card h3 {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
        }

        .stat-card .value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-card .change {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-medium);
        }

        .form-control {
            padding: 0.75rem;
            border: 2px solid var(--border-light);
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
        }

        .table-responsive {
            overflow-x: auto;
            margin-top: 1rem;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .table th {
            background: var(--primary-green);
            color: white;
            padding: 1rem;
            text-align: right;
            font-weight: 600;
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border-light);
        }

        .table tbody tr:hover {
            background: #f5f5f5;
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .alerts-container {
            margin-bottom: 2rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-danger {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .calculation-demo {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid var(--primary-green);
        }

        .calculation-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-light);
        }

        .calculation-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.1rem;
            color: var(--primary-green);
        }

        .sidebar-demo {
            position: fixed;
            right: -300px;
            top: 0;
            height: 100vh;
            width: 300px;
            background: linear-gradient(180deg, var(--primary-green) 0%, #1b5e20 100%);
            color: white;
            transition: right 0.3s ease;
            z-index: 1000;
            padding: 2rem 0;
        }

        .sidebar-demo.active {
            right: 0;
        }

        .sidebar-item {
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar-item:hover {
            background: rgba(255,255,255,0.1);
        }

        .sidebar-item.active {
            background: var(--accent-lime);
            border-right: 4px solid white;
        }

        .test-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid var(--border-light);
            border-radius: 6px;
            box-shadow: var(--shadow-lg);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid var(--border-light);
            transition: background 0.3s ease;
        }

        .suggestion-item:hover {
            background: var(--background-light);
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .form-group {
            position: relative;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-light);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-medium);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--border-light);
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-green), var(--accent-lime));
            transition: width 0.3s ease;
            width: 0%;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #e8f5e8;
            color: var(--primary-green);
        }

        .status-late {
            background: #ffebee;
            color: var(--warning-red);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }

        .btn-edit {
            background: var(--info-blue);
            color: white;
        }

        .btn-delete {
            background: var(--warning-red);
            color: white;
        }

        .btn-view {
            background: var(--accent-lime);
            color: white;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .test-controls {
                flex-direction: column;
            }

            .modal-content {
                width: 95%;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- الرأس -->
    <header class="main-header">
        <div class="header-content">
            <div class="header-title">
                <h1>🥬 سوق الجملة للخضر والغلال</h1>
                <p>جرزونة - نقطة البيع #14 - بيه الغالي | اختبار النظام المحدث</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="toggleSidebar()">
                    📱 القائمة الجانبية
                </button>
                <button class="btn btn-primary" onclick="runAllTests()">
                    🧪 تشغيل جميع الاختبارات
                </button>
            </div>
        </div>
    </header>

    <!-- القائمة الجانبية التجريبية -->
    <div class="sidebar-demo" id="sidebar">
        <div class="sidebar-item active">
            <span>📊</span> لوحة المعلومات
        </div>
        <div class="sidebar-item">
            <span>👥</span> قائمة الحرفاء
        </div>
        <div class="sidebar-item">
            <span>🛒</span> قائمة المشتريات
        </div>
        <div class="sidebar-item">
            <span>🏭</span> قائمة الموردين
        </div>
        <div class="sidebar-item">
            <span>📦</span> قائمة البضائع
        </div>
        <div class="sidebar-item">
            <span>📋</span> قائمة الصناديق
        </div>
        <div class="sidebar-item">
            <span>🧾</span> فواتير الموردين
        </div>
        <div class="sidebar-item">
            <span>⚙️</span> الإعدادات
        </div>
    </div>

    <div class="container">
        <!-- اختبار Dashboard -->
        <div class="test-section">
            <h2>🎯 اختبار لوحة المعلومات (Dashboard)</h2>
            
            <div class="alerts-container">
                <div class="alert alert-warning">
                    <span>⚠️</span>
                    <span>مخزون الطماطم منخفض (15 كغ متبقي)</span>
                </div>
                <div class="alert alert-info">
                    <span>ℹ️</span>
                    <span>تم إضافة فاتورة جديدة للمورد أحمد الخضر</span>
                </div>
                <div class="alert alert-danger">
                    <span>🔴</span>
                    <span>دين متأخر للحريف محمد علي (500 د.ت)</span>
                </div>
            </div>

            <div class="dashboard-grid">
                <div class="stat-card">
                    <h3>إجمالي المبيعات اليوم</h3>
                    <div class="value" id="totalSales">2,450.75 د.ت</div>
                    <div class="change">+12% من الأمس</div>
                </div>
                <div class="stat-card">
                    <h3>عدد العمليات</h3>
                    <div class="value" id="operationsCount">23</div>
                    <div class="change">+5 عمليات جديدة</div>
                </div>
                <div class="stat-card">
                    <h3>عدد الحرفاء النشطين</h3>
                    <div class="value" id="activeCustomers">18</div>
                    <div class="change">3 حرفاء جدد</div>
                </div>
                <div class="stat-card">
                    <h3>الصناديق المستخدمة</h3>
                    <div class="value" id="boxesUsed">145</div>
                    <div class="change">+25 صندوق</div>
                </div>
            </div>
        </div>

        <!-- اختبار الحسابات التلقائية -->
        <div class="test-section">
            <h2>🧮 اختبار الحسابات التلقائية</h2>
            
            <div class="test-controls">
                <button class="btn btn-primary" onclick="testWeightCalculation()">
                    اختبار حساب الوزن الصافي
                </button>
                <button class="btn btn-primary" onclick="testDepositCalculation()">
                    اختبار حساب الرهن
                </button>
                <button class="btn btn-primary" onclick="testInvoiceCalculation()">
                    اختبار حساب الفاتورة
                </button>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label>الوزن القائم (كغ)</label>
                    <input type="number" class="form-control" id="grossWeight" value="100" step="0.1">
                </div>
                <div class="form-group">
                    <label>عدد الصناديق</label>
                    <input type="number" class="form-control" id="boxCount" value="10">
                </div>
                <div class="form-group">
                    <label>نوع الصندوق</label>
                    <select class="form-control" id="boxType">
                        <option value="الصندوق الكبير">الصندوق الكبير (2kg)</option>
                        <option value="Plato">Plato (1.5kg)</option>
                        <option value="Lam plus">Lam plus (0.75kg)</option>
                        <option value="بلا حمولة">بلا حمولة</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>سعر الكيلو (د.ت)</label>
                    <input type="number" class="form-control" id="pricePerKg" value="1.5" step="0.01">
                </div>
            </div>

            <div class="calculation-demo" id="calculationResults">
                <div class="calculation-row">
                    <span>الوزن الصافي:</span>
                    <span id="netWeight">80.0 كغ</span>
                </div>
                <div class="calculation-row">
                    <span>المبلغ الجملي:</span>
                    <span id="totalAmount">120.00 د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>الرهن:</span>
                    <span id="deposit">2.00 د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>المجموع النهائي:</span>
                    <span id="finalTotal">122.00 د.ت</span>
                </div>
            </div>
        </div>

        <!-- اختبار الجداول التفاعلية -->
        <div class="test-section">
            <h2>📊 اختبار الجداول التفاعلية</h2>

            <div class="test-controls">
                <button class="btn btn-primary" onclick="showCustomersTable()">
                    👥 جدول الحرفاء
                </button>
                <button class="btn btn-primary" onclick="showSuppliersTable()">
                    🏭 جدول الموردين
                </button>
                <button class="btn btn-primary" onclick="showPurchasesTable()">
                    🛒 جدول المشتريات
                </button>
                <button class="btn btn-primary" onclick="addNewCustomer()">
                    ➕ إضافة حريف جديد
                </button>
            </div>

            <div id="tablesContainer">
                <!-- سيتم ملء الجداول هنا ديناميكياً -->
            </div>
        </div>

        <!-- اختبار نظام البحث الذكي -->
        <div class="test-section">
            <h2>🔍 اختبار البحث الذكي والتعمير التلقائي</h2>

            <div class="form-grid">
                <div class="form-group">
                    <label>البحث في الحرفاء (اكتب أول حرف)</label>
                    <input type="text" class="form-control" id="customerSearch" placeholder="ابدأ بكتابة اسم الحريف..." autocomplete="off">
                    <div id="customerSuggestions" class="suggestions-dropdown"></div>
                </div>
                <div class="form-group">
                    <label>البحث في الموردين</label>
                    <input type="text" class="form-control" id="supplierSearch" placeholder="ابدأ بكتابة اسم المورد..." autocomplete="off">
                    <div id="supplierSuggestions" class="suggestions-dropdown"></div>
                </div>
                <div class="form-group">
                    <label>البحث في البضائع</label>
                    <input type="text" class="form-control" id="productSearch" placeholder="ابدأ بكتابة نوع البضاعة..." autocomplete="off">
                    <div id="productSuggestions" class="suggestions-dropdown"></div>
                </div>
            </div>
        </div>

        <!-- اختبار فاتورة المورد -->
        <div class="test-section">
            <h2>🧾 اختبار فاتورة المورد</h2>
            
            <div class="form-grid">
                <div class="form-group">
                    <label>اسم المورد</label>
                    <select class="form-control">
                        <option>أحمد الخضر</option>
                        <option>مورد الغلال</option>
                        <option>سوق الجملة المركزي</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>التاريخ</label>
                    <input type="date" class="form-control" value="2025-01-15">
                </div>
            </div>

            <div class="calculation-demo">
                <div class="calculation-row">
                    <span>المبلغ الخام:</span>
                    <span>1,000.00 د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>4% من الخام:</span>
                    <span>40.00 د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>7% من الخام:</span>
                    <span>70.00 د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>الحمولة:</span>
                    <span>25.00 د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>الصافي:</span>
                    <span>865.00 د.ت</span>
                </div>
            </div>
        </div>

        <!-- اختبار نظام التقارير والإحصائيات -->
        <div class="test-section">
            <h2>📈 اختبار التقارير والإحصائيات</h2>

            <div class="test-controls">
                <button class="btn btn-primary" onclick="generateDailyReport()">
                    📊 تقرير يومي
                </button>
                <button class="btn btn-primary" onclick="generateCustomerReport()">
                    👥 تقرير الحرفاء
                </button>
                <button class="btn btn-primary" onclick="generateSupplierReport()">
                    🏭 تقرير الموردين
                </button>
                <button class="btn btn-primary" onclick="exportData()">
                    💾 تصدير البيانات
                </button>
            </div>

            <div id="reportsContainer">
                <!-- سيتم عرض التقارير هنا -->
            </div>
        </div>

        <!-- اختبار نظام التنبيهات الذكية -->
        <div class="test-section">
            <h2>🔔 اختبار التنبيهات الذكية</h2>

            <div class="test-controls">
                <button class="btn btn-primary" onclick="checkLowStock()">
                    📦 فحص المخزون المنخفض
                </button>
                <button class="btn btn-primary" onclick="checkOverdueDebts()">
                    💰 فحص الديون المتأخرة
                </button>
                <button class="btn btn-primary" onclick="checkUnusedBoxes()">
                    📋 فحص الصناديق غير المستخدمة
                </button>
                <button class="btn btn-primary" onclick="generateSmartAlerts()">
                    🤖 تنبيهات ذكية شاملة
                </button>
            </div>

            <div id="alertsContainer">
                <!-- سيتم عرض التنبيهات هنا -->
            </div>
        </div>
    </div>

    <!-- Modal للإضافة والتعديل -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">إضافة عنصر جديد</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalBody">
                <!-- سيتم ملء المحتوى ديناميكياً -->
            </div>
        </div>
    </div>

    <script>
        // بيانات الصناديق المحدثة
        const boxTypes = {
            'الصندوق الكبير': { weight: 2.0, deposit: 0.200, load: 0.200 },
            'Plato': { weight: 1.5, deposit: 0.200, load: 0.200 },
            'Lam plus': { weight: 0.75, deposit: 0.170, load: 0.170 },
            '4 Carro': { weight: 0.75, deposit: 0.170, load: 0.170 },
            'Scarface': { weight: 0.75, deposit: 0.170, load: 0.170 },
            'Lam demi': { weight: 0.7, deposit: 0.170, load: 0.170 },
            'Lam mini': { weight: 0.6, deposit: 0.170, load: 0.170 },
            'Carton': { weight: 0, deposit: 0.300, load: 0.300 },
            'بلا حمولة': { weight: 0, deposit: 0, load: 0 }
        };

        // بيانات تجريبية للحرفاء
        let customers = [
            { id: 1, name: 'أحمد محمد', debt: 500.00, boxes: 15, deposits: 30.00, lastOperation: '2025-01-15', status: 'متأخر' },
            { id: 2, name: 'فاطمة الزهراء', debt: 0.00, boxes: 8, deposits: 16.00, lastOperation: '2025-01-14', status: 'نشط' },
            { id: 3, name: 'محمد علي', debt: 750.00, boxes: 22, deposits: 44.00, lastOperation: '2025-01-13', status: 'متأخر' },
            { id: 4, name: 'سارة أحمد', debt: 200.00, boxes: 12, deposits: 24.00, lastOperation: '2025-01-15', status: 'نشط' }
        ];

        // بيانات تجريبية للموردين
        let suppliers = [
            { id: 1, name: 'أحمد الخضر', boxesSold: 150, productType: 'خضر متنوعة', netWeight: 1200, avgPrice: 1.25 },
            { id: 2, name: 'مورد الغلال', boxesSold: 80, productType: 'غلال موسمية', netWeight: 800, avgPrice: 2.10 },
            { id: 3, name: 'سوق الجملة المركزي', boxesSold: 200, productType: 'خضر وغلال', netWeight: 1800, avgPrice: 1.45 }
        ];

        // بيانات تجريبية للمشتريات
        let purchases = [
            { id: 1, date: '2025-01-15', customer: 'أحمد محمد', supplier: 'أحمد الخضر', product: 'طماطم', grossWeight: 100, boxCount: 10, boxType: 'الصندوق الكبير', pricePerKg: 1.5, paid: false },
            { id: 2, date: '2025-01-15', customer: 'فاطمة الزهراء', supplier: 'مورد الغلال', product: 'تفاح', grossWeight: 80, boxCount: 8, boxType: 'Plato', pricePerKg: 2.0, paid: true },
            { id: 3, date: '2025-01-14', customer: 'محمد علي', supplier: 'سوق الجملة المركزي', product: 'خيار', grossWeight: 120, boxCount: 15, boxType: 'Lam plus', pricePerKg: 1.2, paid: false }
        ];

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        }

        // حساب الوزن الصافي
        function calculateNetWeight() {
            const grossWeight = parseFloat(document.getElementById('grossWeight').value) || 0;
            const boxCount = parseInt(document.getElementById('boxCount').value) || 0;
            const boxType = document.getElementById('boxType').value;
            
            const boxWeight = boxTypes[boxType]?.weight || 0;
            const netWeight = Math.max(0, grossWeight - (boxWeight * boxCount));
            
            return netWeight;
        }

        // حساب الرهن
        function calculateDeposit() {
            const boxCount = parseInt(document.getElementById('boxCount').value) || 0;
            const boxType = document.getElementById('boxType').value;
            const netWeight = calculateNetWeight();
            
            if (boxType === 'بلا حمولة') {
                return netWeight * 10;
            }
            
            const depositRate = boxTypes[boxType]?.deposit || 0;
            return boxCount * depositRate;
        }

        // حساب المبلغ الإجمالي
        function calculateTotalAmount() {
            const netWeight = calculateNetWeight();
            const pricePerKg = parseFloat(document.getElementById('pricePerKg').value) || 0;
            return netWeight * pricePerKg;
        }

        // تحديث النتائج
        function updateCalculations() {
            const netWeight = calculateNetWeight();
            const totalAmount = calculateTotalAmount();
            const deposit = calculateDeposit();
            const finalTotal = totalAmount + deposit;

            document.getElementById('netWeight').textContent = netWeight.toFixed(1) + ' كغ';
            document.getElementById('totalAmount').textContent = totalAmount.toFixed(2) + ' د.ت';
            document.getElementById('deposit').textContent = deposit.toFixed(2) + ' د.ت';
            document.getElementById('finalTotal').textContent = finalTotal.toFixed(2) + ' د.ت';
        }

        // اختبار حساب الوزن
        function testWeightCalculation() {
            updateCalculations();
            alert('✅ تم اختبار حساب الوزن الصافي بنجاح!');
        }

        // اختبار حساب الرهن
        function testDepositCalculation() {
            updateCalculations();
            alert('✅ تم اختبار حساب الرهن بنجاح!');
        }

        // اختبار حساب الفاتورة
        function testInvoiceCalculation() {
            const grossAmount = 1000;
            const fourPercent = grossAmount * 0.04;
            const sevenPercent = grossAmount * 0.07;
            const loadCost = 25;
            const netAmount = grossAmount - (fourPercent + sevenPercent + loadCost);
            
            console.log('حساب الفاتورة:', {
                grossAmount,
                fourPercent,
                sevenPercent,
                loadCost,
                netAmount
            });
            
            alert('✅ تم اختبار حساب الفاتورة بنجاح! النتيجة: ' + netAmount + ' د.ت');
        }

        // تشغيل جميع الاختبارات
        function runAllTests() {
            updateCalculations();

            // محاكاة تحديث البيانات
            const stats = {
                totalSales: Math.random() * 5000 + 2000,
                operations: Math.floor(Math.random() * 50 + 20),
                customers: Math.floor(Math.random() * 30 + 15),
                boxes: Math.floor(Math.random() * 200 + 100)
            };

            document.getElementById('totalSales').textContent = stats.totalSales.toFixed(2) + ' د.ت';
            document.getElementById('operationsCount').textContent = stats.operations;
            document.getElementById('activeCustomers').textContent = stats.customers;
            document.getElementById('boxesUsed').textContent = stats.boxes;

            // تشغيل اختبارات شاملة
            showProgress('تشغيل الاختبارات الشاملة...', 0);

            setTimeout(() => {
                showProgress('اختبار الحسابات التلقائية...', 20);
                testAllCalculations();
            }, 500);

            setTimeout(() => {
                showProgress('اختبار الجداول التفاعلية...', 40);
                showCustomersTable();
            }, 1000);

            setTimeout(() => {
                showProgress('اختبار البحث الذكي...', 60);
                testSmartSearch();
            }, 1500);

            setTimeout(() => {
                showProgress('اختبار التنبيهات...', 80);
                generateSmartAlerts();
            }, 2000);

            setTimeout(() => {
                showProgress('اكتمال الاختبارات...', 100);
                setTimeout(() => {
                    hideProgress();
                    alert('🎉 تم تشغيل جميع الاختبارات بنجاح!\n\n' +
                          '✅ Dashboard محدث\n' +
                          '✅ الحسابات التلقائية تعمل\n' +
                          '✅ الجداول التفاعلية نشطة\n' +
                          '✅ البحث الذكي يعمل\n' +
                          '✅ التنبيهات الذكية نشطة\n' +
                          '✅ القائمة الجانبية متجاوبة');
                }, 500);
            }, 2500);
        }

        // عرض شريط التقدم
        function showProgress(message, percentage) {
            let progressContainer = document.getElementById('progressContainer');
            if (!progressContainer) {
                progressContainer = document.createElement('div');
                progressContainer.id = 'progressContainer';
                progressContainer.innerHTML = `
                    <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                                background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-lg);
                                z-index: 3000; min-width: 300px; text-align: center;">
                        <h3 id="progressMessage">${message}</h3>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <p id="progressPercentage">${percentage}%</p>
                    </div>
                `;
                document.body.appendChild(progressContainer);
            } else {
                document.getElementById('progressMessage').textContent = message;
                document.getElementById('progressPercentage').textContent = percentage + '%';
            }
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // إخفاء شريط التقدم
        function hideProgress() {
            const progressContainer = document.getElementById('progressContainer');
            if (progressContainer) {
                progressContainer.remove();
            }
        }

        // وظائف الجداول التفاعلية
        function showCustomersTable() {
            const container = document.getElementById('tablesContainer');
            container.innerHTML = `
                <h3>👥 جدول الحرفاء</h3>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الحريف</th>
                                <th>الديون</th>
                                <th>الصناديق</th>
                                <th>الرهون</th>
                                <th>آخر عملية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${customers.map(customer => `
                                <tr>
                                    <td>${customer.name}</td>
                                    <td style="color: ${customer.debt > 0 ? 'var(--warning-red)' : 'var(--accent-lime)'};">
                                        ${customer.debt > 0 ? '🔴' : '✅'} ${customer.debt.toFixed(2)} د.ت
                                    </td>
                                    <td>📦 ${customer.boxes}</td>
                                    <td>💰 ${customer.deposits.toFixed(2)} د.ت</td>
                                    <td>${customer.lastOperation}</td>
                                    <td>
                                        <span class="status-badge ${customer.status === 'نشط' ? 'status-active' : 'status-late'}">
                                            ${customer.status}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-view" onclick="viewCustomer(${customer.id})">👁️</button>
                                            <button class="btn btn-sm btn-edit" onclick="editCustomer(${customer.id})">✏️</button>
                                            <button class="btn btn-sm btn-delete" onclick="deleteCustomer(${customer.id})">🗑️</button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        function showSuppliersTable() {
            const container = document.getElementById('tablesContainer');
            container.innerHTML = `
                <h3>🏭 جدول الموردين</h3>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم المورد</th>
                                <th>عدد الصناديق المباعة</th>
                                <th>نوع البضاعة</th>
                                <th>الوزن الصافي</th>
                                <th>متوسط السعر</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${suppliers.map(supplier => `
                                <tr>
                                    <td>${supplier.name}</td>
                                    <td>📦 ${supplier.boxesSold}</td>
                                    <td>${supplier.productType}</td>
                                    <td>⚖️ ${supplier.netWeight} كغ</td>
                                    <td>💰 ${supplier.avgPrice.toFixed(2)} د.ت</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-sm btn-view" onclick="viewSupplier(${supplier.id})">👁️</button>
                                            <button class="btn btn-sm btn-edit" onclick="editSupplier(${supplier.id})">✏️</button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        function showPurchasesTable() {
            const container = document.getElementById('tablesContainer');
            container.innerHTML = `
                <h3>🛒 جدول المشتريات</h3>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>الحريف</th>
                                <th>المورد</th>
                                <th>البضاعة</th>
                                <th>الوزن القائم</th>
                                <th>الوزن الصافي</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${purchases.map(purchase => {
                                const netWeight = purchase.grossWeight - (purchase.boxCount * boxTypes[purchase.boxType].weight);
                                const totalAmount = netWeight * purchase.pricePerKg;
                                return `
                                    <tr>
                                        <td>${purchase.date}</td>
                                        <td>${purchase.customer}</td>
                                        <td>${purchase.supplier}</td>
                                        <td>${purchase.product}</td>
                                        <td>⚖️ ${purchase.grossWeight} كغ</td>
                                        <td>⚖️ ${netWeight.toFixed(1)} كغ</td>
                                        <td>💰 ${totalAmount.toFixed(2)} د.ت</td>
                                        <td>
                                            <span class="status-badge ${purchase.paid ? 'status-active' : 'status-late'}">
                                                ${purchase.paid ? 'مدفوع' : 'غير مدفوع'}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button class="btn btn-sm btn-view" onclick="viewPurchase(${purchase.id})">👁️</button>
                                                <button class="btn btn-sm btn-edit" onclick="editPurchase(${purchase.id})">✏️</button>
                                                <button class="btn btn-sm btn-delete" onclick="deletePurchase(${purchase.id})">🗑️</button>
                                            </div>
                                        </td>
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        // وظائف البحث الذكي
        function setupSmartSearch() {
            const customerSearch = document.getElementById('customerSearch');
            const supplierSearch = document.getElementById('supplierSearch');
            const productSearch = document.getElementById('productSearch');

            // البحث في الحرفاء
            customerSearch.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                const suggestions = customers.filter(customer =>
                    customer.name.toLowerCase().includes(query)
                ).slice(0, 5);

                showSuggestions('customerSuggestions', suggestions, 'name', (customer) => {
                    customerSearch.value = customer.name;
                    hideSuggestions('customerSuggestions');
                });
            });

            // البحث في الموردين
            supplierSearch.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                const suggestions = suppliers.filter(supplier =>
                    supplier.name.toLowerCase().includes(query)
                ).slice(0, 5);

                showSuggestions('supplierSuggestions', suggestions, 'name', (supplier) => {
                    supplierSearch.value = supplier.name;
                    hideSuggestions('supplierSuggestions');
                });
            });

            // البحث في البضائع
            const products = ['طماطم', 'خيار', 'تفاح', 'برتقال', 'بطاطا', 'جزر', 'فلفل', 'باذنجان'];
            productSearch.addEventListener('input', function() {
                const query = this.value.toLowerCase();
                const suggestions = products.filter(product =>
                    product.toLowerCase().includes(query)
                ).slice(0, 5);

                showSuggestions('productSuggestions', suggestions.map(p => ({name: p})), 'name', (product) => {
                    productSearch.value = product.name;
                    hideSuggestions('productSuggestions');
                });
            });
        }

        function showSuggestions(containerId, suggestions, displayField, onSelect) {
            const container = document.getElementById(containerId);
            if (suggestions.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.innerHTML = suggestions.map(item =>
                `<div class="suggestion-item" onclick="selectSuggestion(this, '${containerId}', ${JSON.stringify(item).replace(/"/g, '&quot;')})">${item[displayField]}</div>`
            ).join('');
            container.style.display = 'block';
        }

        function hideSuggestions(containerId) {
            document.getElementById(containerId).style.display = 'none';
        }

        function selectSuggestion(element, containerId, item) {
            const parsedItem = JSON.parse(element.getAttribute('onclick').match(/selectSuggestion\(this, '[^']+', (.+)\)/)[1].replace(/&quot;/g, '"'));

            if (containerId === 'customerSuggestions') {
                document.getElementById('customerSearch').value = parsedItem.name;
            } else if (containerId === 'supplierSuggestions') {
                document.getElementById('supplierSearch').value = parsedItem.name;
            } else if (containerId === 'productSuggestions') {
                document.getElementById('productSearch').value = parsedItem.name;
            }

            hideSuggestions(containerId);
        }

        // وظائف التقارير
        function generateDailyReport() {
            const container = document.getElementById('reportsContainer');
            const totalSales = purchases.reduce((sum, purchase) => {
                const netWeight = purchase.grossWeight - (purchase.boxCount * boxTypes[purchase.boxType].weight);
                return sum + (netWeight * purchase.pricePerKg);
            }, 0);

            container.innerHTML = `
                <div class="calculation-demo">
                    <h4>📊 التقرير اليومي - ${new Date().toLocaleDateString('ar-TN')}</h4>
                    <div class="calculation-row">
                        <span>إجمالي العمليات:</span>
                        <span>${purchases.length} عملية</span>
                    </div>
                    <div class="calculation-row">
                        <span>إجمالي المبيعات:</span>
                        <span>${totalSales.toFixed(2)} د.ت</span>
                    </div>
                    <div class="calculation-row">
                        <span>عدد الحرفاء النشطين:</span>
                        <span>${customers.filter(c => c.status === 'نشط').length} حريف</span>
                    </div>
                    <div class="calculation-row">
                        <span>إجمالي الديون:</span>
                        <span>${customers.reduce((sum, c) => sum + c.debt, 0).toFixed(2)} د.ت</span>
                    </div>
                </div>
            `;
        }

        function generateSmartAlerts() {
            const container = document.getElementById('alertsContainer');
            const alerts = [];

            // فحص الديون المتأخرة
            const overdueCustomers = customers.filter(c => c.debt > 0 && c.status === 'متأخر');
            if (overdueCustomers.length > 0) {
                alerts.push({
                    type: 'danger',
                    icon: '🔴',
                    message: `${overdueCustomers.length} حريف لديهم ديون متأخرة بقيمة ${overdueCustomers.reduce((sum, c) => sum + c.debt, 0).toFixed(2)} د.ت`
                });
            }

            // فحص المخزون
            alerts.push({
                type: 'warning',
                icon: '📦',
                message: 'مخزون الطماطم منخفض - يُنصح بالتزود'
            });

            // فحص الصناديق
            const totalBoxes = customers.reduce((sum, c) => sum + c.boxes, 0);
            if (totalBoxes > 200) {
                alerts.push({
                    type: 'info',
                    icon: 'ℹ️',
                    message: `إجمالي الصناديق المستخدمة: ${totalBoxes} صندوق`
                });
            }

            container.innerHTML = alerts.map(alert => `
                <div class="alert alert-${alert.type}">
                    <span>${alert.icon}</span>
                    <span>${alert.message}</span>
                </div>
            `).join('');
        }

        // وظائف Modal
        function openModal(title, content) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            document.getElementById('modal').style.display = 'flex';
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        function addNewCustomer() {
            const content = `
                <div class="form-grid">
                    <div class="form-group">
                        <label>اسم الحريف</label>
                        <input type="text" class="form-control" id="newCustomerName" placeholder="أدخل اسم الحريف">
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" class="form-control" id="newCustomerPhone" placeholder="أدخل رقم الهاتف">
                    </div>
                    <div class="form-group">
                        <label>العنوان</label>
                        <input type="text" class="form-control" id="newCustomerAddress" placeholder="أدخل العنوان">
                    </div>
                </div>
                <div style="margin-top: 1.5rem; text-align: center;">
                    <button class="btn btn-primary" onclick="saveNewCustomer()">💾 حفظ الحريف</button>
                    <button class="btn" onclick="closeModal()" style="margin-right: 1rem;">❌ إلغاء</button>
                </div>
            `;
            openModal('➕ إضافة حريف جديد', content);
        }

        function saveNewCustomer() {
            const name = document.getElementById('newCustomerName').value;
            if (name.trim()) {
                const newCustomer = {
                    id: customers.length + 1,
                    name: name.trim(),
                    debt: 0,
                    boxes: 0,
                    deposits: 0,
                    lastOperation: new Date().toISOString().split('T')[0],
                    status: 'نشط'
                };
                customers.push(newCustomer);
                closeModal();
                showCustomersTable();
                alert('✅ تم إضافة الحريف بنجاح!');
            } else {
                alert('⚠️ يرجى إدخال اسم الحريف');
            }
        }

        // وظائف اختبار شاملة
        function testAllCalculations() {
            updateCalculations();
            testWeightCalculation();
            testDepositCalculation();
            testInvoiceCalculation();
        }

        function testSmartSearch() {
            setupSmartSearch();
            // محاكاة كتابة في البحث
            const customerSearch = document.getElementById('customerSearch');
            customerSearch.value = 'أحمد';
            customerSearch.dispatchEvent(new Event('input'));
        }

        // إضافة مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث الحسابات عند تغيير القيم
            ['grossWeight', 'boxCount', 'boxType', 'pricePerKg'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', updateCalculations);
                    element.addEventListener('change', updateCalculations);
                }
            });

            // تحديث أولي
            updateCalculations();

            // إعداد البحث الذكي
            setupSmartSearch();

            // إضافة تأثيرات للقائمة الجانبية
            document.querySelectorAll('.sidebar-item').forEach(item => {
                item.addEventListener('click', function() {
                    document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // إغلاق القائمة الجانبية عند النقر خارجها
            document.addEventListener('click', function(e) {
                const sidebar = document.getElementById('sidebar');
                if (!sidebar.contains(e.target) && !e.target.closest('.btn')) {
                    sidebar.classList.remove('active');
                }
            });

            // إغلاق اقتراحات البحث عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.form-group')) {
                    ['customerSuggestions', 'supplierSuggestions', 'productSuggestions'].forEach(id => {
                        hideSuggestions(id);
                    });
                }
            });

            // إغلاق Modal عند النقر خارجها
            document.getElementById('modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });

            // عرض جدول الحرفاء افتراضياً
            showCustomersTable();
        });

        // وظائف إضافية للإجراءات
        function viewCustomer(id) { alert(`عرض تفاصيل الحريف رقم ${id}`); }
        function editCustomer(id) { alert(`تعديل الحريف رقم ${id}`); }
        function deleteCustomer(id) {
            if (confirm('هل أنت متأكد من حذف هذا الحريف؟')) {
                customers = customers.filter(c => c.id !== id);
                showCustomersTable();
                alert('تم حذف الحريف بنجاح');
            }
        }
        function viewSupplier(id) { alert(`عرض تفاصيل المورد رقم ${id}`); }
        function editSupplier(id) { alert(`تعديل المورد رقم ${id}`); }
        function viewPurchase(id) { alert(`عرض تفاصيل المشترى رقم ${id}`); }
        function editPurchase(id) { alert(`تعديل المشترى رقم ${id}`); }
        function deletePurchase(id) {
            if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
                purchases = purchases.filter(p => p.id !== id);
                showPurchasesTable();
                alert('تم حذف العملية بنجاح');
            }
        }

        function generateCustomerReport() { alert('تم إنشاء تقرير الحرفاء'); }
        function generateSupplierReport() { alert('تم إنشاء تقرير الموردين'); }
        function exportData() { alert('تم تصدير البيانات بنجاح'); }
        function checkLowStock() { alert('تم فحص المخزون المنخفض'); }
        function checkOverdueDebts() { alert('تم فحص الديون المتأخرة'); }
        function checkUnusedBoxes() { alert('تم فحص الصناديق غير المستخدمة'); }
    </script>
</body>
</html>
