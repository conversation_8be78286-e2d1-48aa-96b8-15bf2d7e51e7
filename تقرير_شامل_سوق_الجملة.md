# التقرير الشامل لبرنامج سوق الجملة للخضر والغلال

## معلومات المشروع
- **اسم المشروع**: سوق الجملة للخضر والغلال بجرزونة – نقطة بيع عدد 14 - بيه الغالي
- **المطور**: أسامة الصولي (oussema souli)
- **تاريخ التطوير**: 2025
- **حقوق الملكية**: oussema souli

## الهدف من البرنامج
تنظيم وميكنة عمليات البيع والشراء اليومية في السوق بطريقة احترافية، دقيقة، وسريعة لتحسين كفاءة العمل وضمان دقة المعاملات التجارية.

## الوصف التقني للنظام

### 1. البنية التقنية
- **نوع التطبيق**: تطبيق ويب تفاعلي (Single Page Application)
- **التقنيات المستخدمة**:
  - HTML5 للهيكل الأساسي
  - CSS3 للتصميم والتنسيق
  - JavaScript (ES6+) للوظائف التفاعلية
- **اللغة**: العربية مع دعم RTL (Right-to-Left)
- **التوافق**: متوافق مع جميع المتصفحات الحديثة

### 2. الملفات الأساسية
- `index.html`: الصفحة الرئيسية وهيكل الواجهة
- `app.js`: منطق التطبيق والوظائف التفاعلية
- `data.js`: بيانات المنتجات والإعدادات الأساسية
- `style.css`: تصميم وتنسيق الواجهة

## الوظائف الرئيسية

### 1. لوحة المعلومات (Dashboard)
- **عرض المبيعات اليومية**: إجمالي المبيعات بالدينار التونسي
- **عدد المنتجات المتاحة**: إحصائية فورية للمنتجات في النظام
- **تحديث تلقائي**: تحديث البيانات فور إجراء أي عملية

### 2. إدارة المنتجات
#### إضافة المنتجات:
- اسم المنتج
- الفئة (خضر، غلال، بهارات، أخرى)
- وحدة القياس (كيلو، صندوق، كرتونة، علبة، حبة)
- السعر بالدينار التونسي
- الكمية المتاحة في المخزون

#### تعديل المنتجات:
- إمكانية تعديل جميع بيانات المنتج
- حفظ التغييرات فوريًا
- إلغاء التعديل والعودة للحالة السابقة

#### حذف المنتجات:
- حذف آمن مع رسالة تأكيد
- منع الحذف العرضي

### 3. عمليات البيع
#### إنشاء فاتورة جديدة:
- اختيار المنتج من قائمة منسدلة
- تحديد الكمية المطلوبة
- التحقق من توفر الكمية في المخزون
- إضافة المنتجات إلى الفاتورة

#### إدارة الفاتورة:
- عرض تفصيلي للمنتجات المضافة
- حساب المجموع الفرعي لكل منتج
- حساب الإجمالي العام
- إمكانية حذف منتجات من الفاتورة

#### إتمام البيع:
- تحديث المخزون تلقائيًا
- تسجيل عملية البيع في السجلات
- إعادة تعيين الفاتورة للعملية التالية

## البيانات الأساسية

### المنتجات المدرجة مسبقًا:
1. **الخضر**:
   - طماطم: 1.2 د.ت/كيلو (مخزون: 50)
   - بطاطا: 0.8 د.ت/كيلو (مخزون: 100)
   - بصل: 0.9 د.ت/كيلو (مخزون: 80)
   - جزر: 1.1 د.ت/كيلو (مخزون: 60)
   - خيار: 1.0 د.ت/كيلو (مخزون: 45)

2. **الغلال**:
   - تفاح: 1.5 د.ت/كيلو (مخزون: 40)
   - برتقال: 1.3 د.ت/كيلو (مخزون: 70)
   - موز: 1.8 د.ت/كيلو (مخزون: 30)

### الفئات المتاحة:
- خضر
- غلال
- بهارات
- أخرى

### وحدات القياس:
- كيلو
- صندوق
- كرتونة
- علبة
- حبة

## المميزات العامة للبرنامج

### 1. الأداء والسرعة
- **تسريع النظام والعمليات الحسابية**: محرك حسابي محسن لمعالجة سريعة للعمليات
- **دعم لنظام حساب تلقائي**: حساب فوري للمجاميع والإجماليات دون تدخل يدوي
- **استجابة فورية**: تحديث البيانات والواجهة في الوقت الفعلي

### 2. التصميم والواجهة
- **تصميم احترافي مبني على**:
  - **الجداول**: عرض منظم ومرتب للبيانات
  - **الكروت**: تنظيم المعلومات في بطاقات تفاعلية
  - **الأيقونات**: رموز بصرية واضحة ومفهومة
  - **الألوان**: نظام ألوان متناسق ومريح للعين
- **واجهة مستخدم متكاملة وسلسة**: تجربة استخدام موحدة وسهلة
- **القوائم الجانبية عمودية على الجانب الأيمن**: تخطيط مناسب للغة العربية

### 3. التكامل والترابط
- **جميع الأقسام مترابطة وتؤثر على بعضها البعض**:
  - تحديث المخزون يؤثر على المبيعات
  - عمليات البيع تحدث المبيعات اليومية
  - إضافة المنتجات تظهر في قوائم البيع
  - التغييرات تنعكس فوراً على لوحة المعلومات

### 4. إدارة البيانات
- **دعم حفظ البيانات بشكل دائم**: ضمان عدم فقدان المعلومات
- **نظام تاريخ دقيق إلى مستوى الثواني**: تسجيل زمني مفصل لجميع العمليات
- **يميز عمليات الشراء حسب "اليوم" و"الأمس"**: تصنيف زمني ذكي للمعاملات

### 5. المميزات التقنية

#### واجهة المستخدم:
- **تصميم متجاوب**: يتكيف مع جميع أحجام الشاشات
- **سهولة الاستخدام**: واجهة بديهية وبسيطة
- **دعم اللغة العربية**: تخطيط RTL كامل
- **ألوان متناسقة**: نظام ألوان احترافي

#### الأمان والموثوقية:
- **التحقق من البيانات**: فحص صحة المدخلات
- **منع الأخطاء**: رسائل تحذيرية للعمليات الحساسة
- **حفظ البيانات**: حفظ محلي للبيانات

#### الأداء:
- **سرعة الاستجابة**: تحديث فوري للواجهة
- **استهلاك ذاكرة منخفض**: كود محسن ومرتب
- **عمل بدون إنترنت**: لا يتطلب اتصال بالشبكة

## تطبيق المميزات في النظام

### 1. النظام الحسابي التلقائي
```javascript
// حساب تلقائي للمجاميع في الفاتورة
function updateCart() {
    let total = 0;
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal; // حساب تلقائي فوري
    });
    cartTotalElement.textContent = total.toFixed(2) + ' د.ت';
}
```

### 2. نظام التاريخ الدقيق
```javascript
// تسجيل العمليات بدقة الثواني
dailySales.push({
    date: new Date(), // تاريخ دقيق إلى مستوى الثواني
    timestamp: Date.now(),
    items: [...cart],
    total: totalAmount
});
```

### 3. التمييز الزمني للعمليات
```javascript
// تصنيف المبيعات حسب اليوم والأمس
function categorizeByDate(sales) {
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();

    return {
        today: sales.filter(sale => new Date(sale.date).toDateString() === today),
        yesterday: sales.filter(sale => new Date(sale.date).toDateString() === yesterday)
    };
}
```

### 4. الترابط بين الأقسام
```javascript
// مثال على الترابط: تحديث المخزون يؤثر على المبيعات
function completeSale() {
    cart.forEach(item => {
        // تحديث المخزون
        const product = products.find(p => p.id == item.id);
        product.stock -= item.quantity;

        // تحديث المبيعات
        dailySales.push(saleRecord);

        // تحديث لوحة المعلومات
        updateDashboard();
    });
}
```

### 5. التصميم المبني على الكروت والجداول
```css
/* تصميم الكروت */
.card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    transition: transform 0.3s ease;
}

/* تصميم الجداول */
table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
}
```

### 6. القوائم الجانبية العمودية
```css
/* تخطيط القوائم الجانبية */
.sidebar {
    position: fixed;
    right: 0; /* على الجانب الأيمن للعربية */
    top: 0;
    height: 100vh;
    width: 250px;
    background: var(--primary-color);
    direction: rtl;
}
```

## خطة التطوير المستقبلية

### المرحلة الثانية (مقترحة):
1. **إدارة المخزون المتقدمة**:
   - تنبيهات نفاد المخزون
   - تتبع تواريخ الانتهاء
   - إدارة الموردين

2. **نظام التقارير**:
   - تقارير المبيعات اليومية/الشهرية
   - تحليل الأرباح
   - إحصائيات المنتجات الأكثر مبيعًا

3. **إدارة العملاء**:
   - قاعدة بيانات العملاء
   - نظام الائتمان
   - تاريخ المعاملات

4. **الطباعة والتصدير**:
   - طباعة الفواتير
   - تصدير التقارير
   - نسخ احتياطية

### المرحلة الثالثة (مقترحة):
1. **قاعدة البيانات**:
   - حفظ دائم للبيانات
   - نسخ احتياطية تلقائية
   - مزامنة البيانات

2. **الشبكة والمشاركة**:
   - نظام متعدد المستخدمين
   - مشاركة البيانات بين النقاط
   - إدارة الصلاحيات

## التوصيات

### للاستخدام الأمثل:
1. **التدريب**: تدريب المستخدمين على النظام
2. **النسخ الاحتياطية**: حفظ نسخ احتياطية دورية
3. **التحديث**: متابعة التحديثات والتحسينات
4. **الصيانة**: فحص دوري للنظام

### للتطوير:
1. **اختبار شامل**: اختبار جميع الوظائف
2. **تحسين الأداء**: مراجعة وتحسين الكود
3. **إضافة مميزات**: تطوير وظائف جديدة حسب الحاجة
4. **الأمان**: تعزيز إجراءات الأمان

## الخلاصة

يعتبر هذا البرنامج حلاً متكاملاً وعمليًا لإدارة عمليات البيع في سوق الجملة للخضر والغلال. يتميز بالبساطة والفعالية، ويوفر أساسًا قويًا للتطوير المستقبلي. النظام مصمم ليكون سهل الاستخدام ومناسب لبيئة العمل في الأسواق التونسية.

---

**تم إعداد هذا التقرير بواسطة**: أسامة الصولي  
**التاريخ**: 2025  
**الإصدار**: 1.0
