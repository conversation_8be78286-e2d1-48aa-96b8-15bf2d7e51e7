# 🔗 العلاقات المترابطة بين أقسام النظام

## 📋 نظرة عامة

عند تسجيل أي عملية شراء في النظام، يتم تحديث جميع الأقسام المترابطة تلقائياً لضمان تزامن البيانات والدقة في الحسابات.

---

## 🛒 تأثير عملية الشراء على الأقسام

### 1️⃣ **قسم الحرفاء** 👥
**التأثير المباشر:**
- **إضافة/تحديث الديون** حسب حالة الدفع
- **تسجيل الرهون** إذا كان مفعل
- **تحديث تاريخ آخر عملية**
- **زيادة عداد العمليات**

**التفاصيل:**
```javascript
// حساب الدين المتبقي
const remainingDebt = totalAmount - paidAmount;

// تحديث بيانات العميل
if (existingClient) {
    client.totalDebt += remainingDebt;
    client.totalDeposits += depositAmount;
    client.purchaseCount += 1;
} else {
    // إضافة عميل جديد
    createNewClient(clientData);
}
```

**حالات الدفع:**
- **🟢 مدفوع كاملاً** ← لا يوجد دين
- **🟡 دفع جزئي** ← دين = المبلغ الكلي - المدفوع
- **🔴 غير مدفوع** ← دين = المبلغ الكلي

---

### 2️⃣ **قسم البضائع** 🥬
**التأثير المباشر:**
- **اقتطاع الكميات المباعة** من المخزون
- **تسجيل عدد الصناديق المستخدمة**
- **تحديث تاريخ آخر بيع**
- **ربط البضاعة بالموردين**

**التفاصيل:**
```javascript
// لكل بضاعة في العملية
products.forEach(item => {
    const netWeight = grossWeight - (boxCount * boxWeight);
    
    product.soldQuantity += netWeight;
    product.usedBoxes += boxCount;
    product.lastSaleDate = purchaseDate;
    
    // إضافة المورد إذا لم يكن موجود
    if (!product.suppliers.includes(item.supplier)) {
        product.suppliers.push(item.supplier);
    }
});
```

**البيانات المحفوظة:**
- **الكمية المباعة** (بالكيلوغرام)
- **عدد الصناديق المستخدمة**
- **قائمة الموردين**
- **تاريخ آخر بيع**

---

### 3️⃣ **قسم الموردين** 🏪
**التأثير المباشر:**
- **تسجيل إجمالي المبيعات** لكل مورد
- **حساب الوزن الكلي المباع**
- **تحديث قائمة البضائع**
- **تسجيل تاريخ آخر معاملة**

**التفاصيل:**
```javascript
// تجميع البيانات حسب المورد
const supplierData = {};
purchase.items.forEach(item => {
    if (!supplierData[item.supplier]) {
        supplierData[item.supplier] = {
            totalAmount: 0,
            totalWeight: 0,
            products: []
        };
    }
    
    supplierData[item.supplier].totalAmount += item.total;
    supplierData[item.supplier].totalWeight += item.netWeight;
    supplierData[item.supplier].products.push(item.product);
});
```

**الإحصائيات المحفوظة:**
- **إجمالي المبيعات** (بالدينار)
- **إجمالي الوزن** (بالكيلوغرام)
- **قائمة البضائع المباعة**
- **تاريخ آخر معاملة**

---

### 4️⃣ **قسم الصناديق** 📦
**التأثير المباشر:**
- **تتبع استخدام كل نوع صندوق**
- **حساب الوزن الصافي** (الوزن القائم - وزن الصناديق)
- **تحديد قيمة الرهن** حسب نوع الصندوق
- **تسجيل تاريخ آخر استخدام**

**التفاصيل:**
```javascript
// أنواع الصناديق ومواصفاتها
const boxTypes = {
    'الصندوق الكبير': { weight: 2.0, deposit: 10.0 },
    'Plato': { weight: 1.5, deposit: 10.0 },
    'Lam plus': { weight: 0.75, deposit: 3.0 },
    'Lam': { weight: 0.5, deposit: 3.0 },
    'Barket': { weight: 0.3, deposit: 3.0 },
    'Barket صغير': { weight: 0.2, deposit: 3.0 },
    'Caisse': { weight: 1.0, deposit: 3.0 },
    'Caisse صغير': { weight: 0.8, deposit: 3.0 },
    'بلا حمولة': { weight: 0, deposit: 0.01 } // رهن خاص
};

// حساب الوزن الصافي
const netWeight = grossWeight - (boxCount * boxWeight);

// حساب الرهن
const deposit = boxType === 'بلا حمولة' ? 
    netWeight * 0.01 : boxCount * boxDeposit;
```

**البيانات المتتبعة:**
- **عدد الاستخدامات** لكل نوع
- **الوزن والرهن** لكل نوع
- **تاريخ آخر استخدام**

---

### 5️⃣ **فواتير الموردين** 🧾
**التأثير المباشر:**
- **إنشاء فاتورة تلقائية** لكل مورد
- **حساب المبلغ الإجمالي والصافي**
- **تطبيق نسب الخصم** (4% + 7% = 11%)
- **تحديد حالة الفاتورة** (معلقة/مدفوعة)

**التفاصيل:**
```javascript
// حساب فاتورة المورد
const grossAmount = netWeight * pricePerKg;
const deductionPercentage = 11; // 4% + 7%
const netAmount = grossAmount * (1 - deductionPercentage / 100);

// إنشاء فاتورة
const invoice = {
    supplierName: item.supplier,
    purchaseId: purchase.id,
    items: supplierItems,
    grossAmount: totalGrossAmount,
    netAmount: totalNetAmount,
    deductionPercentage: 11,
    status: 'pending'
};
```

**مكونات الفاتورة:**
- **المبلغ الإجمالي** (قبل الخصم)
- **المبلغ الصافي** (بعد الخصم)
- **نسبة الخصم** (11%)
- **تفاصيل البضائع**
- **حالة الدفع**

---

## 🔄 مخطط التدفق

```mermaid
graph TD
    A[عملية شراء جديدة] --> B{تحديث الأقسام}
    
    B --> C[👥 الحرفاء]
    B --> D[🥬 البضائع]
    B --> E[🏪 الموردين]
    B --> F[📦 الصناديق]
    B --> G[🧾 فواتير الموردين]
    
    C --> C1[إضافة/خصم الديون]
    C --> C2[تسجيل الرهون]
    C --> C3[تحديث الإحصائيات]
    
    D --> D1[اقتطاع الكميات]
    D --> D2[تسجيل الصناديق]
    D --> D3[ربط الموردين]
    
    E --> E1[حساب المبيعات]
    E --> E2[تسجيل الأوزان]
    E --> E3[تحديث البضائع]
    
    F --> F1[تتبع الاستخدام]
    F --> F2[حساب الوزن الصافي]
    F --> F3[تحديد الرهن]
    
    G --> G1[إنشاء فاتورة]
    G --> G2[حساب الخصومات]
    G --> G3[تحديد الحالة]
```

---

## 💾 تخزين البيانات

جميع البيانات يتم حفظها في **localStorage** مؤقتاً، ويمكن ربطها بقاعدة بيانات حقيقية لاحقاً:

```javascript
// مفاتيح التخزين
localStorage.setItem('clients', JSON.stringify(clientsData));
localStorage.setItem('products', JSON.stringify(productsData));
localStorage.setItem('suppliers', JSON.stringify(suppliersData));
localStorage.setItem('boxes', JSON.stringify(boxesData));
localStorage.setItem('supplierInvoices', JSON.stringify(invoicesData));
```

---

## ⚡ المميزات

### 🔄 **التحديث التلقائي**
- لا حاجة لتحديث يدوي للأقسام
- ضمان تزامن البيانات
- منع الأخطاء البشرية

### 📊 **الدقة في الحسابات**
- حسابات تلقائية للأوزان والمبالغ
- تطبيق نسب الخصم بدقة
- تتبع دقيق للمخزون

### 🎯 **الشمولية**
- تغطية جميع جوانب العملية
- ربط جميع الأقسام ببعضها
- تقارير شاملة ودقيقة

### 🚀 **الكفاءة**
- تحديث فوري للبيانات
- عدم تكرار العمليات
- أداء سريع ومستقر

---

## 🔧 التطبيق العملي

عند تسجيل عملية شراء، يتم استدعاء الدالة الرئيسية:

```javascript
const updateRelatedSections = (purchase) => {
    updateClientSection(purchase);      // تحديث الحرفاء
    updateProductsSection(purchase);    // تحديث البضائع
    updateSuppliersSection(purchase);   // تحديث الموردين
    updateBoxesSection(purchase);       // تحديث الصناديق
    updateSupplierInvoices(purchase);   // تحديث الفواتير
};
```

هذا يضمن أن جميع الأقسام تبقى متزامنة ومحدثة تلقائياً! 🎯✨
