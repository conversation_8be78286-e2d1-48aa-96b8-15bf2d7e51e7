<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار واجهة الحرفاء</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .search-highlight {
            background-color: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-paid {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-unpaid {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .action-btn {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
            margin: 0 1px;
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        .btn-debts { background: #f97316; color: white; }
        .btn-boxes { background: #3b82f6; color: white; }
        .btn-deposits { background: #8b5cf6; color: white; }
        .btn-delete { background: #ef4444; color: white; }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            max-width: 90%;
            max-height: 90%;
            overflow-y: auto;
            position: relative;
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-weight: bold;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-6 max-w-7xl">
        <!-- العنوان الرئيسي -->
        <div class="gradient-bg text-white p-6 rounded-xl mb-6 shadow-lg">
            <h1 class="text-3xl font-bold mb-2">👥 إدارة الحرفاء</h1>
            <p class="text-blue-100">إدارة شاملة لقائمة الحرفاء والعملاء</p>
        </div>

        <!-- شريط البحث والإضافة -->
        <div class="bg-white p-4 rounded-xl shadow-md mb-6">
            <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
                <div class="flex-1 relative">
                    <input
                        type="text"
                        id="searchInput"
                        placeholder="🔍 البحث عن حريف (الاسم أو رقم الهاتف)..."
                        class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none text-lg"
                        oninput="filterClients()"
                    />
                    <button
                        id="clearSearch"
                        onclick="clearSearch()"
                        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        style="display: none;"
                    >
                        ✖
                    </button>
                </div>
                <button
                    onclick="toggleAddForm()"
                    class="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 font-semibold shadow-md"
                >
                    ➕ إضافة حريف جديد
                </button>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-grid mt-4">
                <div class="stat-card" style="background: #dbeafe;">
                    <div class="stat-number text-blue-600" id="totalClients">5</div>
                    <div class="stat-label">إجمالي الحرفاء</div>
                </div>
                <div class="stat-card" style="background: #dcfce7;">
                    <div class="stat-number text-green-600" id="totalOperations">53</div>
                    <div class="stat-label">إجمالي العمليات</div>
                </div>
                <div class="stat-card" style="background: #f3e8ff;">
                    <div class="stat-number text-purple-600" id="totalSales">8,951.50 د.ت</div>
                    <div class="stat-label">إجمالي المبيعات</div>
                </div>
                <div class="stat-card" style="background: #fee2e2;">
                    <div class="stat-number text-red-600" id="totalDebts">900.50 د.ت</div>
                    <div class="stat-label">الديون المستحقة</div>
                </div>
            </div>
        </div>

        <!-- نموذج إضافة حريف جديد -->
        <div id="addForm" class="bg-white p-6 rounded-xl shadow-lg mb-6 border-2 border-green-200" style="display: none;">
            <h3 class="text-xl font-bold mb-4 text-green-700">➕ إضافة حريف جديد</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-semibold mb-2 text-gray-700">اسم الحريف *</label>
                    <input
                        type="text"
                        id="clientName"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-green-500 focus:outline-none"
                        placeholder="أدخل اسم الحريف الكامل"
                    />
                </div>
                <div>
                    <label class="block text-sm font-semibold mb-2 text-gray-700">رقم الهاتف</label>
                    <input
                        type="text"
                        id="clientPhone"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-green-500 focus:outline-none"
                        placeholder="رقم الهاتف"
                    />
                </div>
                <div>
                    <label class="block text-sm font-semibold mb-2 text-gray-700">العنوان</label>
                    <input
                        type="text"
                        id="clientAddress"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-green-500 focus:outline-none"
                        placeholder="عنوان الحريف"
                    />
                </div>
                <div>
                    <label class="block text-sm font-semibold mb-2 text-gray-700">ملاحظات</label>
                    <input
                        type="text"
                        id="clientNotes"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-green-500 focus:outline-none"
                        placeholder="ملاحظات إضافية"
                    />
                </div>
            </div>
            <div class="flex gap-3 mt-4">
                <button
                    onclick="addClient()"
                    class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors font-semibold"
                >
                    ✅ حفظ الحريف
                </button>
                <button
                    onclick="toggleAddForm()"
                    class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                >
                    ❌ إلغاء
                </button>
            </div>
        </div>

        <!-- قائمة الحرفاء -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="bg-gray-50 p-4 border-b">
                <h3 class="text-lg font-bold text-gray-800">
                    📋 قائمة الحرفاء (<span id="clientCount">5</span>)
                </h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full" id="clientsTable">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="p-3 text-center font-semibold">#</th>
                            <th class="p-3 text-right font-semibold">👤 الاسم</th>
                            <th class="p-3 text-right font-semibold">📞 الهاتف</th>
                            <th class="p-3 text-right font-semibold">📍 العنوان</th>
                            <th class="p-3 text-right font-semibold">📅 تاريخ التسجيل</th>
                            <th class="p-3 text-right font-semibold">🛒 العمليات</th>
                            <th class="p-3 text-right font-semibold">💰 المبلغ الكلي</th>
                            <th class="p-3 text-right font-semibold">⚠️ الديون</th>
                            <th class="p-3 text-center font-semibold">🔧 الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="clientsTableBody">
                        <!-- سيتم ملء البيانات بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- رسالة عدم وجود نتائج -->
        <div id="noResults" class="bg-white rounded-xl shadow-lg p-8 text-center text-gray-500" style="display: none;">
            <div class="text-4xl mb-4">🔍</div>
            <p class="text-lg">لا توجد نتائج للبحث</p>
        </div>
    </div>

    <!-- المودال لعرض التفاصيل -->
    <div id="detailsModal" class="modal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal()">✖</button>
            <div id="modalContent">
                <!-- سيتم ملء المحتوى بواسطة JavaScript -->
            </div>
        </div>
    </div>

    <script>
        // بيانات تجريبية للحرفاء
        let clients = [
            {
                id: 1,
                name: 'أحمد محمد الصالح',
                phone: '98765432',
                address: 'حي النصر، تونس',
                registrationDate: '2024-01-15',
                totalPurchases: 15,
                totalAmount: 2450.50,
                unpaidAmount: 320.00,
                notes: 'حريف مميز'
            },
            {
                id: 2,
                name: 'فاطمة بن علي',
                phone: '97654321',
                address: 'المنزه، تونس',
                registrationDate: '2024-02-20',
                totalPurchases: 8,
                totalAmount: 1200.75,
                unpaidAmount: 0,
                notes: 'دفع نقدي دائماً'
            },
            {
                id: 3,
                name: 'محمد الهادي',
                phone: '96543210',
                address: 'باردو، تونس',
                registrationDate: '2024-03-10',
                totalPurchases: 22,
                totalAmount: 3800.25,
                unpaidAmount: 580.50,
                notes: 'يفضل الدفع الآجل'
            },
            {
                id: 4,
                name: 'زينب الطاهر',
                phone: '95432109',
                address: 'سيدي بوسعيد، تونس',
                registrationDate: '2024-02-05',
                totalPurchases: 5,
                totalAmount: 850.00,
                unpaidAmount: 0,
                notes: 'حريف جديد'
            },
            {
                id: 5,
                name: 'يوسف بن سالم',
                phone: '94321098',
                address: 'قرطاج، تونس',
                registrationDate: '2024-01-30',
                totalPurchases: 3,
                totalAmount: 650.00,
                unpaidAmount: 0,
                notes: 'عميل مؤسسي'
            }
        ];

        let filteredClients = [...clients];

        // تحديث الإحصائيات
        function updateStats() {
            document.getElementById('totalClients').textContent = clients.length;
            document.getElementById('totalOperations').textContent = clients.reduce((sum, c) => sum + c.totalPurchases, 0);
            document.getElementById('totalSales').textContent = clients.reduce((sum, c) => sum + c.totalAmount, 0).toFixed(2) + ' د.ت';
            document.getElementById('totalDebts').textContent = clients.reduce((sum, c) => sum + c.unpaidAmount, 0).toFixed(2) + ' د.ت';
        }

        // عرض الحرفاء في الجدول
        function renderClients() {
            const tbody = document.getElementById('clientsTableBody');
            const noResults = document.getElementById('noResults');
            const clientsTable = document.querySelector('.bg-white.rounded-xl.shadow-lg.overflow-hidden');
            
            if (filteredClients.length === 0) {
                clientsTable.style.display = 'none';
                noResults.style.display = 'block';
                return;
            }

            clientsTable.style.display = 'block';
            noResults.style.display = 'none';

            tbody.innerHTML = filteredClients.map((client, index) => `
                <tr class="border-b hover:bg-gray-50 transition-colors">
                    <td class="p-3 text-center font-bold text-gray-500">${index + 1}</td>
                    <td class="p-3 font-semibold text-blue-700">${client.name}</td>
                    <td class="p-3 text-gray-600">${client.phone || '-'}</td>
                    <td class="p-3 text-gray-600">${client.address || '-'}</td>
                    <td class="p-3 text-gray-600">${client.registrationDate}</td>
                    <td class="p-3 text-center">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                            ${client.totalPurchases}
                        </span>
                    </td>
                    <td class="p-3 text-green-600 font-semibold">${client.totalAmount.toFixed(2)} د.ت</td>
                    <td class="p-3">
                        <span class="status-badge ${client.unpaidAmount > 0 ? 'status-unpaid' : 'status-paid'}">
                            ${client.unpaidAmount > 0 ? `${client.unpaidAmount.toFixed(2)} د.ت` : 'مسدد'}
                        </span>
                    </td>
                    <td class="p-3">
                        <div class="flex gap-1 justify-center">
                            <button class="action-btn btn-debts" onclick="showDebts(${client.id})" title="عرض الديون">🧾</button>
                            <button class="action-btn btn-boxes" onclick="showBoxes(${client.id})" title="عرض الصناديق">📦</button>
                            <button class="action-btn btn-deposits" onclick="showDeposits(${client.id})" title="عرض الرهون">🪙</button>
                            <button class="action-btn btn-delete" onclick="deleteClient(${client.id})" title="حذف الحريف">🗑️</button>
                        </div>
                    </td>
                </tr>
            `).join('');

            document.getElementById('clientCount').textContent = filteredClients.length;
        }

        // تصفية الحرفاء
        function filterClients() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const clearBtn = document.getElementById('clearSearch');
            
            if (searchTerm) {
                clearBtn.style.display = 'block';
                filteredClients = clients.filter(client =>
                    client.name.toLowerCase().includes(searchTerm) ||
                    (client.phone && client.phone.includes(searchTerm))
                );
            } else {
                clearBtn.style.display = 'none';
                filteredClients = [...clients];
            }
            
            renderClients();
        }

        // مسح البحث
        function clearSearch() {
            document.getElementById('searchInput').value = '';
            filterClients();
        }

        // إظهار/إخفاء نموذج الإضافة
        function toggleAddForm() {
            const form = document.getElementById('addForm');
            form.style.display = form.style.display === 'none' ? 'block' : 'none';
            
            if (form.style.display === 'none') {
                // مسح الحقول
                document.getElementById('clientName').value = '';
                document.getElementById('clientPhone').value = '';
                document.getElementById('clientAddress').value = '';
                document.getElementById('clientNotes').value = '';
            }
        }

        // إضافة حريف جديد
        function addClient() {
            const name = document.getElementById('clientName').value.trim();
            const phone = document.getElementById('clientPhone').value.trim();
            const address = document.getElementById('clientAddress').value.trim();
            const notes = document.getElementById('clientNotes').value.trim();

            if (!name) {
                alert('⚠️ يرجى إدخال اسم الحريف');
                return;
            }

            const newClient = {
                id: Date.now(),
                name,
                phone,
                address,
                notes,
                registrationDate: new Date().toISOString().split('T')[0],
                totalPurchases: 0,
                totalAmount: 0,
                unpaidAmount: 0
            };

            clients.push(newClient);
            filteredClients = [...clients];
            renderClients();
            updateStats();
            toggleAddForm();
            alert('✅ تم إضافة الحريف بنجاح');
        }

        // حذف حريف
        function deleteClient(id) {
            if (confirm('هل أنت متأكد من حذف هذا الحريف؟')) {
                clients = clients.filter(c => c.id !== id);
                filteredClients = [...clients];
                renderClients();
                updateStats();
                alert('🗑️ تم حذف الحريف');
            }
        }

        // عرض تفاصيل الديون
        function showDebts(clientId) {
            const client = clients.find(c => c.id === clientId);
            if (!client) return;

            const content = `
                <h2 class="text-2xl font-bold mb-4 text-orange-600">🧾 تفاصيل ديون ${client.name}</h2>
                
                <div class="stats-grid mb-6">
                    <div class="stat-card" style="background: #fee2e2;">
                        <div class="stat-number text-red-600">${client.unpaidAmount.toFixed(2)} د.ت</div>
                        <div class="stat-label">إجمالي الديون</div>
                    </div>
                    <div class="stat-card" style="background: #dcfce7;">
                        <div class="stat-number text-green-600">${(client.totalAmount - client.unpaidAmount).toFixed(2)} د.ت</div>
                        <div class="stat-label">المبلغ المدفوع</div>
                    </div>
                    <div class="stat-card" style="background: #dbeafe;">
                        <div class="stat-number text-blue-600">${client.totalPurchases}</div>
                        <div class="stat-label">عدد العمليات</div>
                    </div>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-bold mb-3">📋 تفاصيل العمليات غير المدفوعة</h4>
                    <div class="space-y-2">
                        <div class="bg-white p-3 rounded border-r-4 border-red-500">
                            <div class="flex justify-between items-center">
                                <span>عملية شراء - 2024-03-15</span>
                                <span class="font-bold text-red-600">150.00 د.ت</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">طماطم، خيار - 25 كغ</div>
                        </div>
                        <div class="bg-white p-3 rounded border-r-4 border-red-500">
                            <div class="flex justify-between items-center">
                                <span>عملية شراء - 2024-03-20</span>
                                <span class="font-bold text-red-600">170.00 د.ت</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">تفاح، برتقال - 30 كغ</div>
                        </div>
                    </div>
                </div>
            `;

            showModal(content);
        }

        // عرض تفاصيل الصناديق
        function showBoxes(clientId) {
            const client = clients.find(c => c.id === clientId);
            if (!client) return;

            const content = `
                <h2 class="text-2xl font-bold mb-4 text-blue-600">📦 تفاصيل صناديق ${client.name}</h2>
                
                <div class="stats-grid mb-6">
                    <div class="stat-card" style="background: #dbeafe;">
                        <div class="stat-number text-blue-600">45</div>
                        <div class="stat-label">صناديق مستعملة</div>
                    </div>
                    <div class="stat-card" style="background: #dcfce7;">
                        <div class="stat-number text-green-600">38</div>
                        <div class="stat-label">صناديق مرجعة</div>
                    </div>
                    <div class="stat-card" style="background: #fed7aa;">
                        <div class="stat-number text-orange-600">7</div>
                        <div class="stat-label">صناديق متبقية</div>
                    </div>
                    <div class="stat-card" style="background: #f3e8ff;">
                        <div class="stat-number text-purple-600">70.0 د.ت</div>
                        <div class="stat-label">قيمة الصناديق</div>
                    </div>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-bold mb-3">📦 تفاصيل الصناديق المستعملة</h4>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm bg-white rounded">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="p-2 text-right">نوع الصندوق</th>
                                    <th class="p-2 text-center">العدد المستعمل</th>
                                    <th class="p-2 text-center">العدد المرجع</th>
                                    <th class="p-2 text-center">المتبقي</th>
                                    <th class="p-2 text-center">القيمة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b">
                                    <td class="p-2">الصندوق الكبير</td>
                                    <td class="p-2 text-center">15</td>
                                    <td class="p-2 text-center">12</td>
                                    <td class="p-2 text-center text-orange-600 font-bold">3</td>
                                    <td class="p-2 text-center">30.0 د.ت</td>
                                </tr>
                                <tr class="border-b">
                                    <td class="p-2">Plato</td>
                                    <td class="p-2 text-center">20</td>
                                    <td class="p-2 text-center">18</td>
                                    <td class="p-2 text-center text-orange-600 font-bold">2</td>
                                    <td class="p-2 text-center">20.0 د.ت</td>
                                </tr>
                                <tr class="border-b">
                                    <td class="p-2">Lam plus</td>
                                    <td class="p-2 text-center">10</td>
                                    <td class="p-2 text-center">8</td>
                                    <td class="p-2 text-center text-orange-600 font-bold">2</td>
                                    <td class="p-2 text-center">6.0 د.ت</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            showModal(content);
        }

        // عرض تفاصيل الرهون
        function showDeposits(clientId) {
            const client = clients.find(c => c.id === clientId);
            if (!client) return;

            const content = `
                <h2 class="text-2xl font-bold mb-4 text-purple-600">🪙 تفاصيل رهون ${client.name}</h2>
                
                <div class="stats-grid mb-6">
                    <div class="stat-card" style="background: #f3e8ff;">
                        <div class="stat-number text-purple-600">245.0 د.ت</div>
                        <div class="stat-label">إجمالي الرهون</div>
                    </div>
                    <div class="stat-card" style="background: #dcfce7;">
                        <div class="stat-number text-green-600">180.0 د.ت</div>
                        <div class="stat-label">رهون مسترجعة</div>
                    </div>
                    <div class="stat-card" style="background: #fed7aa;">
                        <div class="stat-number text-orange-600">65.0 د.ت</div>
                        <div class="stat-label">رهون متبقية</div>
                    </div>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-bold mb-3">🪙 سجل الرهون</h4>
                    <div class="space-y-2">
                        <div class="bg-white p-3 rounded border-r-4 border-purple-500">
                            <div class="flex justify-between items-center">
                                <span>رهن صناديق - 2024-03-15</span>
                                <span class="font-bold text-purple-600">+50.0 د.ت</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">5 صناديق كبيرة</div>
                        </div>
                        <div class="bg-white p-3 rounded border-r-4 border-green-500">
                            <div class="flex justify-between items-center">
                                <span>استرجاع رهن - 2024-03-18</span>
                                <span class="font-bold text-green-600">-30.0 د.ت</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">3 صناديق كبيرة مرجعة</div>
                        </div>
                        <div class="bg-white p-3 rounded border-r-4 border-purple-500">
                            <div class="flex justify-between items-center">
                                <span>رهن صناديق - 2024-03-20</span>
                                <span class="font-bold text-purple-600">+45.0 د.ت</span>
                            </div>
                            <div class="text-sm text-gray-600 mt-1">15 صندوق Lam plus</div>
                        </div>
                    </div>
                </div>
            `;

            showModal(content);
        }

        // عرض المودال
        function showModal(content) {
            document.getElementById('modalContent').innerHTML = content;
            document.getElementById('detailsModal').classList.add('show');
        }

        // إغلاق المودال
        function closeModal() {
            document.getElementById('detailsModal').classList.remove('show');
        }

        // إغلاق المودال عند النقر خارجه
        document.getElementById('detailsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            renderClients();
            updateStats();
        });
    </script>
</body>
</html>
