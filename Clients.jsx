import React, { useState, useEffect } from 'react';

const Clients = () => {
  const [clients, setClients] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedClient, setSelectedClient] = useState(null);
  const [viewMode, setViewMode] = useState('list'); // 'list', 'debts', 'boxes', 'deposits'
  
  // نموذج بيانات الحريف الجديد
  const [newClient, setNewClient] = useState({
    name: '',
    phone: '',
    address: '',
    notes: ''
  });

  // بيانات تجريبية للحرفاء
  const sampleClients = [
    {
      id: 1,
      name: 'أحمد محمد الصالح',
      phone: '98765432',
      address: 'حي النصر، تونس',
      registrationDate: '2024-01-15',
      totalPurchases: 15,
      totalAmount: 2450.50,
      unpaidAmount: 320.00,
      notes: 'حريف مميز'
    },
    {
      id: 2,
      name: 'فاطمة بن علي',
      phone: '97654321',
      address: 'المنزه، تونس',
      registrationDate: '2024-02-20',
      totalPurchases: 8,
      totalAmount: 1200.75,
      unpaidAmount: 0,
      notes: 'دفع نقدي دائماً'
    },
    {
      id: 3,
      name: 'محمد الهادي',
      phone: '96543210',
      address: 'باردو، تونس',
      registrationDate: '2024-03-10',
      totalPurchases: 22,
      totalAmount: 3800.25,
      unpaidAmount: 580.50,
      notes: 'يفضل الدفع الآجل'
    }
  ];

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    const savedClients = localStorage.getItem('clients');
    if (savedClients) {
      setClients(JSON.parse(savedClients));
    } else {
      setClients(sampleClients);
      localStorage.setItem('clients', JSON.stringify(sampleClients));
    }
  }, []);

  // حفظ البيانات عند التغيير
  useEffect(() => {
    if (clients.length > 0) {
      localStorage.setItem('clients', JSON.stringify(clients));
    }
  }, [clients]);

  // تصفية الحرفاء حسب البحث
  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.phone.includes(searchTerm)
  );

  // إضافة حريف جديد
  const addClient = () => {
    if (!newClient.name.trim()) {
      alert('⚠️ يرجى إدخال اسم الحريف');
      return;
    }

    const client = {
      id: Date.now(),
      ...newClient,
      registrationDate: new Date().toISOString().split('T')[0],
      totalPurchases: 0,
      totalAmount: 0,
      unpaidAmount: 0
    };

    setClients([...clients, client]);
    setNewClient({ name: '', phone: '', address: '', notes: '' });
    setShowAddForm(false);
    alert('✅ تم إضافة الحريف بنجاح');
  };

  // حذف حريف
  const deleteClient = (id) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الحريف؟')) {
      setClients(clients.filter(c => c.id !== id));
      alert('🗑️ تم حذف الحريف');
    }
  };

  // عرض تفاصيل الديون
  const showClientDebts = (client) => {
    setSelectedClient(client);
    setViewMode('debts');
  };

  // عرض تفاصيل الصناديق
  const showClientBoxes = (client) => {
    setSelectedClient(client);
    setViewMode('boxes');
  };

  // عرض تفاصيل الرهون
  const showClientDeposits = (client) => {
    setSelectedClient(client);
    setViewMode('deposits');
  };

  return (
    <div className="p-6 max-w-7xl mx-auto" dir="rtl">
      {/* العنوان الرئيسي */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-xl mb-6 shadow-lg">
        <h1 className="text-3xl font-bold mb-2">👥 إدارة الحرفاء</h1>
        <p className="text-blue-100">إدارة شاملة لقائمة الحرفاء والعملاء</p>
      </div>

      {/* شريط البحث والإضافة */}
      <div className="bg-white p-4 rounded-xl shadow-md mb-6">
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="🔍 البحث عن حريف (الاسم أو رقم الهاتف)..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none text-lg"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                ✖
              </button>
            )}
          </div>
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 font-semibold shadow-md"
          >
            {showAddForm ? '❌ إلغاء' : '➕ إضافة حريف جديد'}
          </button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
          <div className="bg-blue-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">{clients.length}</div>
            <div className="text-sm text-blue-500">إجمالي الحرفاء</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-600">
              {clients.reduce((sum, c) => sum + c.totalPurchases, 0)}
            </div>
            <div className="text-sm text-green-500">إجمالي العمليات</div>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-purple-600">
              {clients.reduce((sum, c) => sum + c.totalAmount, 0).toFixed(2)} د.ت
            </div>
            <div className="text-sm text-purple-500">إجمالي المبيعات</div>
          </div>
          <div className="bg-red-50 p-3 rounded-lg text-center">
            <div className="text-2xl font-bold text-red-600">
              {clients.reduce((sum, c) => sum + c.unpaidAmount, 0).toFixed(2)} د.ت
            </div>
            <div className="text-sm text-red-500">الديون المستحقة</div>
          </div>
        </div>
      </div>

      {/* نموذج إضافة حريف جديد */}
      {showAddForm && (
        <div className="bg-white p-6 rounded-xl shadow-lg mb-6 border-2 border-green-200">
          <h3 className="text-xl font-bold mb-4 text-green-700">➕ إضافة حريف جديد</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-semibold mb-2 text-gray-700">اسم الحريف *</label>
              <input
                type="text"
                value={newClient.name}
                onChange={(e) => setNewClient({...newClient, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-green-500 focus:outline-none"
                placeholder="أدخل اسم الحريف الكامل"
              />
            </div>
            <div>
              <label className="block text-sm font-semibold mb-2 text-gray-700">رقم الهاتف</label>
              <input
                type="text"
                value={newClient.phone}
                onChange={(e) => setNewClient({...newClient, phone: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-green-500 focus:outline-none"
                placeholder="رقم الهاتف"
              />
            </div>
            <div>
              <label className="block text-sm font-semibold mb-2 text-gray-700">العنوان</label>
              <input
                type="text"
                value={newClient.address}
                onChange={(e) => setNewClient({...newClient, address: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-green-500 focus:outline-none"
                placeholder="عنوان الحريف"
              />
            </div>
            <div>
              <label className="block text-sm font-semibold mb-2 text-gray-700">ملاحظات</label>
              <input
                type="text"
                value={newClient.notes}
                onChange={(e) => setNewClient({...newClient, notes: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-green-500 focus:outline-none"
                placeholder="ملاحظات إضافية"
              />
            </div>
          </div>
          <div className="flex gap-3 mt-4">
            <button
              onClick={addClient}
              className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors font-semibold"
            >
              ✅ حفظ الحريف
            </button>
            <button
              onClick={() => setShowAddForm(false)}
              className="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              ❌ إلغاء
            </button>
          </div>
        </div>
      )}

      {/* عرض قائمة الحرفاء أو التفاصيل */}
      {viewMode === 'list' ? (
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="bg-gray-50 p-4 border-b">
            <h3 className="text-lg font-bold text-gray-800">
              📋 قائمة الحرفاء ({filteredClients.length})
            </h3>
          </div>
          
          {filteredClients.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <div className="text-4xl mb-4">🔍</div>
              <p className="text-lg">لا توجد نتائج للبحث "{searchTerm}"</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="p-3 text-center font-semibold">#</th>
                    <th className="p-3 text-right font-semibold">👤 الاسم</th>
                    <th className="p-3 text-right font-semibold">📞 الهاتف</th>
                    <th className="p-3 text-right font-semibold">📍 العنوان</th>
                    <th className="p-3 text-right font-semibold">📅 تاريخ التسجيل</th>
                    <th className="p-3 text-right font-semibold">🛒 العمليات</th>
                    <th className="p-3 text-right font-semibold">💰 المبلغ الكلي</th>
                    <th className="p-3 text-right font-semibold">⚠️ الديون</th>
                    <th className="p-3 text-center font-semibold">🔧 الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredClients.map((client, index) => (
                    <tr key={client.id} className="border-b hover:bg-gray-50 transition-colors">
                      <td className="p-3 text-center font-bold text-gray-500">{index + 1}</td>
                      <td className="p-3 font-semibold text-blue-700">{client.name}</td>
                      <td className="p-3 text-gray-600">{client.phone || '-'}</td>
                      <td className="p-3 text-gray-600">{client.address || '-'}</td>
                      <td className="p-3 text-gray-600">{client.registrationDate}</td>
                      <td className="p-3 text-center">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                          {client.totalPurchases}
                        </span>
                      </td>
                      <td className="p-3 text-green-600 font-semibold">{client.totalAmount.toFixed(2)} د.ت</td>
                      <td className="p-3">
                        <span className={`px-2 py-1 rounded-full text-sm font-semibold ${
                          client.unpaidAmount > 0
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {client.unpaidAmount > 0 ? `${client.unpaidAmount.toFixed(2)} د.ت` : 'مسدد'}
                        </span>
                      </td>
                      <td className="p-3">
                        <div className="flex gap-1 justify-center">
                          <button
                            onClick={() => showClientDebts(client)}
                            className="bg-orange-500 text-white px-2 py-1 rounded text-xs hover:bg-orange-600 transition-colors"
                            title="عرض الديون"
                          >
                            🧾
                          </button>
                          <button
                            onClick={() => showClientBoxes(client)}
                            className="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600 transition-colors"
                            title="عرض الصناديق"
                          >
                            📦
                          </button>
                          <button
                            onClick={() => showClientDeposits(client)}
                            className="bg-purple-500 text-white px-2 py-1 rounded text-xs hover:bg-purple-600 transition-colors"
                            title="عرض الرهون"
                          >
                            🪙
                          </button>
                          <button
                            onClick={() => deleteClient(client.id)}
                            className="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 transition-colors"
                            title="حذف الحريف"
                          >
                            🗑️
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      ) : (
        // عرض التفاصيل (الديون، الصناديق، الرهون)
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-800">
              {viewMode === 'debts' && '🧾 تفاصيل الديون'}
              {viewMode === 'boxes' && '📦 تفاصيل الصناديق'}
              {viewMode === 'deposits' && '🪙 تفاصيل الرهون'}
              {selectedClient && ` - ${selectedClient.name}`}
            </h3>
            <button
              onClick={() => setViewMode('list')}
              className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              ← العودة للقائمة
            </button>
          </div>

          {/* عرض تفاصيل الديون */}
          {viewMode === 'debts' && selectedClient && (
            <div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-red-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-red-600">{selectedClient.unpaidAmount.toFixed(2)} د.ت</div>
                  <div className="text-sm text-red-500">إجمالي الديون</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {(selectedClient.totalAmount - selectedClient.unpaidAmount).toFixed(2)} د.ت
                  </div>
                  <div className="text-sm text-green-500">المبلغ المدفوع</div>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">{selectedClient.totalPurchases}</div>
                  <div className="text-sm text-blue-500">عدد العمليات</div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-bold mb-3">📋 تفاصيل العمليات غير المدفوعة</h4>
                <div className="space-y-2">
                  <div className="bg-white p-3 rounded border-r-4 border-red-500">
                    <div className="flex justify-between items-center">
                      <span>عملية شراء - 2024-03-15</span>
                      <span className="font-bold text-red-600">150.00 د.ت</span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">طماطم، خيار - 25 كغ</div>
                  </div>
                  <div className="bg-white p-3 rounded border-r-4 border-red-500">
                    <div className="flex justify-between items-center">
                      <span>عملية شراء - 2024-03-20</span>
                      <span className="font-bold text-red-600">170.50 د.ت</span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">تفاح، برتقال - 30 كغ</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* عرض تفاصيل الصناديق */}
          {viewMode === 'boxes' && selectedClient && (
            <div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">45</div>
                  <div className="text-sm text-blue-500">صناديق مستعملة</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">38</div>
                  <div className="text-sm text-green-500">صناديق مرجعة</div>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-orange-600">7</div>
                  <div className="text-sm text-orange-500">صناديق متبقية</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-600">70.0 د.ت</div>
                  <div className="text-sm text-purple-500">قيمة الصناديق</div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-bold mb-3">📦 تفاصيل الصناديق المستعملة</h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead className="bg-white">
                      <tr>
                        <th className="p-2 text-right">نوع الصندوق</th>
                        <th className="p-2 text-center">العدد المستعمل</th>
                        <th className="p-2 text-center">العدد المرجع</th>
                        <th className="p-2 text-center">المتبقي</th>
                        <th className="p-2 text-center">القيمة</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="p-2">الصندوق الكبير</td>
                        <td className="p-2 text-center">15</td>
                        <td className="p-2 text-center">12</td>
                        <td className="p-2 text-center text-orange-600 font-bold">3</td>
                        <td className="p-2 text-center">30.0 د.ت</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2">Plato</td>
                        <td className="p-2 text-center">20</td>
                        <td className="p-2 text-center">18</td>
                        <td className="p-2 text-center text-orange-600 font-bold">2</td>
                        <td className="p-2 text-center">20.0 د.ت</td>
                      </tr>
                      <tr className="border-b">
                        <td className="p-2">Lam plus</td>
                        <td className="p-2 text-center">10</td>
                        <td className="p-2 text-center">8</td>
                        <td className="p-2 text-center text-orange-600 font-bold">2</td>
                        <td className="p-2 text-center">6.0 د.ت</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* عرض تفاصيل الرهون */}
          {viewMode === 'deposits' && selectedClient && (
            <div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-600">245.0 د.ت</div>
                  <div className="text-sm text-purple-500">إجمالي الرهون</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">180.0 د.ت</div>
                  <div className="text-sm text-green-500">رهون مسترجعة</div>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-orange-600">65.0 د.ت</div>
                  <div className="text-sm text-orange-500">رهون متبقية</div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-bold mb-3">🪙 سجل الرهون</h4>
                <div className="space-y-2">
                  <div className="bg-white p-3 rounded border-r-4 border-purple-500">
                    <div className="flex justify-between items-center">
                      <span>رهن صناديق - 2024-03-15</span>
                      <span className="font-bold text-purple-600">+50.0 د.ت</span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">5 صناديق كبيرة</div>
                  </div>
                  <div className="bg-white p-3 rounded border-r-4 border-green-500">
                    <div className="flex justify-between items-center">
                      <span>استرجاع رهن - 2024-03-18</span>
                      <span className="font-bold text-green-600">-30.0 د.ت</span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">3 صناديق كبيرة مرجعة</div>
                  </div>
                  <div className="bg-white p-3 rounded border-r-4 border-purple-500">
                    <div className="flex justify-between items-center">
                      <span>رهن صناديق - 2024-03-20</span>
                      <span className="font-bold text-purple-600">+45.0 د.ت</span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">15 صندوق Lam plus</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Clients;
