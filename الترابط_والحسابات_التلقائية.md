# الترابط والحسابات التلقائية - سوق الجملة للخضر والغلال

## نظرة عامة على الترابط

النظام مصمم بحيث تكون جميع الأقسام مترابطة ومتفاعلة، حيث أي تغيير في قسم واحد ينعكس تلقائياً على الأقسام الأخرى ذات الصلة.

## 🔄 مصفوفة الترابط بين الأقسام

| القسم المؤثر | الأقسام المتأثرة | نوع التأثير |
|-------------|-----------------|-------------|
| المشتريات | الحرفاء، الموردين، البضائع، الصناديق، الفواتير | تحديث شامل |
| فواتير الموردين | الموردين، البضائع، الصناديق | تحديث البيانات والحسابات |
| الحرفاء | الصناديق، الديون | تحديث الرهون والمديونية |
| الإعدادات | جميع الأقسام | تحديث البيانات الأساسية |

## 🧮 الحسابات التلقائية المفصلة

### 1. حسابات المشتريات

#### حساب الوزن الصافي
```javascript
function calculateNetWeight(grossWeight, boxCount, boxType) {
    const boxWeights = {
        'الصندوق الكبير': 2.0,
        'Plato': 1.5,
        'Lam plus': 0.75,
        '4 Carro': 0.75,
        'Scarface': 0.75,
        'Lam demi': 0.7,
        'Lam mini': 0.6,
        'Carton': 0 // يُدخل وزنه يدوياً
    };
    
    const emptyWeight = boxWeights[boxType] ? 
        boxWeights[boxType] * boxCount : 
        getCustomBoxWeight(boxType) * boxCount;
    
    return Math.max(0, grossWeight - emptyWeight);
}
```

#### حساب المبلغ الجملي
```javascript
function calculateTotalAmount(netWeight, pricePerKg) {
    return netWeight * pricePerKg;
}
```

#### حساب الرهن
```javascript
function calculateDeposit(boxCount, boxType) {
    const depositRates = {
        'الصندوق الكبير': 0.200,
        'Plato': 0.200,
        'Lam plus': 0.170,
        '4 Carro': 0.170,
        'Scarface': 0.170,
        'Lam demi': 0.170,
        'Lam mini': 0.170,
        'Carton': 0.300,
        'بلا حمولة': 0 // حساب خاص
    };
    
    if (boxType === 'بلا حمولة') {
        return netWeight * 10; // الوزن الصافي × 10
    }
    
    return boxCount * (depositRates[boxType] || 0);
}
```

### 2. حسابات فواتير الموردين

#### الحسابات الأساسية
```javascript
function calculateSupplierInvoice(items) {
    let calculations = {
        grossAmount: 0,
        fourPercent: 0,
        sevenPercent: 0,
        loadCost: 0,
        netAmount: 0
    };
    
    // حساب المبلغ الخام
    items.forEach(item => {
        calculations.grossAmount += item.netWeight * item.pricePerKg;
    });
    
    // حساب النسب
    calculations.fourPercent = calculations.grossAmount * 0.04;
    calculations.sevenPercent = calculations.grossAmount * 0.07;
    
    // حساب الحمولة
    items.forEach(item => {
        calculations.loadCost += calculateLoadCost(item.boxCount, item.boxType);
    });
    
    // حساب الصافي
    calculations.netAmount = calculations.grossAmount - 
        (calculations.fourPercent + calculations.sevenPercent + calculations.loadCost);
    
    return calculations;
}
```

#### حساب الحمولة
```javascript
function calculateLoadCost(boxCount, boxType) {
    const loadRates = {
        'الصندوق الكبير': 0.200,
        'Plato': 0.200,
        'Lam plus': 0.170,
        '4 Carro': 0.170,
        'Scarface': 0.170,
        'Lam demi': 0.170,
        'Lam mini': 0.170,
        'Carton': 0.300
    };
    
    return boxCount * (loadRates[boxType] || 0);
}
```

## 🔗 آليات الترابط التلقائي

### 1. عند تسجيل عملية شراء جديدة

```javascript
function processPurchase(purchaseData) {
    // 1. تحديث بيانات الحريف
    updateCustomerData(purchaseData.customerId, {
        lastPurchaseDate: new Date(),
        totalPurchases: getCurrentPurchases(purchaseData.customerId) + 1,
        boxes: getCurrentBoxes(purchaseData.customerId) + purchaseData.boxCount,
        deposits: getCurrentDeposits(purchaseData.customerId) + purchaseData.deposit
    });
    
    // 2. تحديث بيانات المورد
    updateSupplierData(purchaseData.supplierId, {
        totalBoxesSold: getCurrentBoxesSold(purchaseData.supplierId) + purchaseData.boxCount,
        totalWeight: getCurrentWeight(purchaseData.supplierId) + purchaseData.netWeight,
        lastSaleDate: new Date()
    });
    
    // 3. تحديث مخزون البضائع
    updateProductInventory(purchaseData.productType, {
        totalBoxes: getCurrentProductBoxes(purchaseData.productType) + purchaseData.boxCount,
        totalWeight: getCurrentProductWeight(purchaseData.productType) + purchaseData.netWeight,
        lastUpdated: new Date()
    });
    
    // 4. تحديث إحصائيات الصناديق
    updateBoxStatistics(purchaseData.boxType, {
        totalUsed: getCurrentBoxUsage(purchaseData.boxType) + purchaseData.boxCount,
        totalDeposits: getCurrentBoxDeposits(purchaseData.boxType) + purchaseData.deposit
    });
    
    // 5. تحديث Dashboard
    updateDashboard();
}
```

### 2. عند تعديل فاتورة مورد

```javascript
function updateSupplierInvoice(invoiceId, newData) {
    const oldInvoice = getInvoiceById(invoiceId);
    
    // حساب الفروقات
    const weightDifference = newData.netWeight - oldInvoice.netWeight;
    const boxDifference = newData.boxCount - oldInvoice.boxCount;
    
    // تحديث بيانات المورد
    updateSupplierStats(oldInvoice.supplierId, {
        totalWeight: getCurrentSupplierWeight(oldInvoice.supplierId) + weightDifference,
        totalBoxes: getCurrentSupplierBoxes(oldInvoice.supplierId) + boxDifference
    });
    
    // تحديث مخزون البضائع
    updateProductInventory(oldInvoice.productType, {
        totalWeight: getCurrentProductWeight(oldInvoice.productType) + weightDifference,
        totalBoxes: getCurrentProductBoxes(oldInvoice.productType) + boxDifference
    });
    
    // إعادة حساب الفاتورة
    const newCalculations = calculateSupplierInvoice([newData]);
    updateInvoice(invoiceId, { ...newData, ...newCalculations });
    
    // تحديث Dashboard
    updateDashboard();
}
```

### 3. نظام التحديث المتسلسل

```javascript
class SystemSync {
    static updateChain(triggerSection, data) {
        const updateMap = {
            'purchases': ['customers', 'suppliers', 'products', 'boxes', 'dashboard'],
            'invoices': ['suppliers', 'products', 'boxes', 'dashboard'],
            'customers': ['boxes', 'dashboard'],
            'settings': ['all']
        };
        
        const sectionsToUpdate = updateMap[triggerSection] || [];
        
        sectionsToUpdate.forEach(section => {
            this.updateSection(section, data);
        });
    }
    
    static updateSection(section, data) {
        switch(section) {
            case 'customers':
                this.updateCustomersSection(data);
                break;
            case 'suppliers':
                this.updateSuppliersSection(data);
                break;
            case 'products':
                this.updateProductsSection(data);
                break;
            case 'boxes':
                this.updateBoxesSection(data);
                break;
            case 'dashboard':
                this.updateDashboard(data);
                break;
            case 'all':
                this.updateAllSections(data);
                break;
        }
    }
}
```

## 📊 تحديث Dashboard التلقائي

### حساب المؤشرات الرئيسية
```javascript
function calculateDashboardStats() {
    const today = new Date().toDateString();
    
    // إجمالي المبيعات اليومية
    const todayPurchases = purchases.filter(p => 
        new Date(p.date).toDateString() === today
    );
    const totalSales = todayPurchases.reduce((sum, p) => sum + p.totalAmount, 0);
    
    // عدد العمليات
    const operationsCount = todayPurchases.length;
    
    // التنبيهات
    const alerts = generateAlerts();
    
    // أبرز الحرفاء
    const topCustomers = getTopCustomers(5);
    
    // أبرز الموردين
    const topSuppliers = getTopSuppliers(5);
    
    return {
        totalSales,
        operationsCount,
        alerts,
        topCustomers,
        topSuppliers
    };
}
```

### إنشاء التنبيهات التلقائية
```javascript
function generateAlerts() {
    const alerts = [];
    
    // تنبيهات المخزون المنخفض
    products.forEach(product => {
        if (product.currentStock < product.minStock) {
            alerts.push({
                type: 'warning',
                message: `مخزون ${product.name} منخفض (${product.currentStock} متبقي)`,
                priority: 'high'
            });
        }
    });
    
    // تنبيهات الديون المتأخرة
    customers.forEach(customer => {
        if (customer.debts > 0 && customer.lastPaymentDate < getDateDaysAgo(30)) {
            alerts.push({
                type: 'danger',
                message: `دين متأخر للحريف ${customer.name} (${customer.debts} د.ت)`,
                priority: 'high'
            });
        }
    });
    
    // تنبيهات الصناديق
    const totalBoxes = calculateTotalBoxesInUse();
    if (totalBoxes > MAX_BOXES_THRESHOLD) {
        alerts.push({
            type: 'info',
            message: `عدد الصناديق المستخدمة مرتفع (${totalBoxes})`,
            priority: 'medium'
        });
    }
    
    return alerts.sort((a, b) => {
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
}
```

## 🔄 التحديث الفوري للواجهة

### نظام الأحداث المتقدم
```javascript
class RealTimeUpdater {
    constructor() {
        this.observers = new Map();
    }
    
    subscribe(section, callback) {
        if (!this.observers.has(section)) {
            this.observers.set(section, []);
        }
        this.observers.get(section).push(callback);
    }
    
    notify(section, data) {
        if (this.observers.has(section)) {
            this.observers.get(section).forEach(callback => {
                callback(data);
            });
        }
    }
    
    notifyAll(data) {
        this.observers.forEach((callbacks, section) => {
            callbacks.forEach(callback => callback(data));
        });
    }
}

// الاستخدام
const updater = new RealTimeUpdater();

// الاشتراك في التحديثات
updater.subscribe('dashboard', updateDashboardDisplay);
updater.subscribe('customers', updateCustomersTable);
updater.subscribe('suppliers', updateSuppliersTable);

// إطلاق التحديثات
function onPurchaseComplete(purchaseData) {
    updater.notify('dashboard', purchaseData);
    updater.notify('customers', purchaseData);
    updater.notify('suppliers', purchaseData);
}
```

---

**ملاحظة**: هذا النظام يضمن التحديث الفوري والدقيق لجميع أجزاء النظام، مما يوفر تجربة متسقة ومتكاملة للمستخدم.
