document.addEventListener('DOMContentLoaded', function() {
    // عناصر الواجهة
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const productContent = document.getElementById('products-content');
    const salesContent = document.getElementById('sales-content');
    const dailySalesElement = document.getElementById('daily-sales');
    const productCountElement = document.getElementById('product-count');
    
    // تحديث لوحة المعلومات
    function updateDashboard() {
        productCountElement.textContent = products.length;
        const totalSales = dailySales.reduce((sum, sale) => sum + sale.total, 0);
        dailySalesElement.textContent = totalSales.toFixed(2) + ' د.ت';
    }
    
    // تبديل التبويبات
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // إزالة النشاط من جميع الأزرار
            tabBtns.forEach(b => b.classList.remove('active'));
            // إخفاء جميع المحتويات
            tabContents.forEach(content => content.style.display = 'none');
            
            // تفعيل الزر المحدد وإظهار محتواه
            btn.classList.add('active');
            const tabId = btn.getAttribute('data-tab');
            document.getElementById(`${tabId}-content`).style.display = 'block';
            currentTab = tabId;
            
            // تحميل المحتوى حسب التبويب
            if (tabId === 'products') renderProducts();
            if (tabId === 'sales') renderSales();
        });
    });
    
    // عرض المنتجات
    function renderProducts() {
        productContent.innerHTML = `
            <div class="product-form">
                <h3>${selectedProduct ? 'تعديل المنتج' : 'إضافة منتج جديد'}</h3>
                <form id="product-form">
                    <input type="hidden" id="product-id" value="${selectedProduct ? selectedProduct.id : ''}">
                    <div class="form-group">
                        <label for="product-name">اسم المنتج:</label>
                        <input type="text" id="product-name" value="${selectedProduct ? selectedProduct.name : ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="product-category">الفئة:</label>
                        <select id="product-category" required>
                            ${categories.map(cat => 
                                `<option value="${cat.id}" ${selectedProduct && selectedProduct.category === cat.name ? 'selected' : ''}>
                                    ${cat.name}
                                </option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="product-unit">وحدة القياس:</label>
                        <select id="product-unit" required>
                            ${units.map(unit => 
                                `<option value="${unit}" ${selectedProduct && selectedProduct.unit === unit ? 'selected' : ''}>
                                    ${unit}
                                </option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="product-price">السعر (د.ت):</label>
                        <input type="number" id="product-price" step="0.01" min="0" value="${selectedProduct ? selectedProduct.price : ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="product-stock">الكمية المتاحة:</label>
                        <input type="number" id="product-stock" min="0" value="${selectedProduct ? selectedProduct.stock : ''}" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit">${selectedProduct ? 'تحديث' : 'إضافة'}</button>
                        ${selectedProduct ? '<button type="button" id="cancel-edit">إلغاء</button>' : ''}
                    </div>
                </form>
            </div>
            <div class="product-list">
                <h3>قائمة المنتجات</h3>
                <table>
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الفئة</th>
                            <th>الوحدة</th>
                            <th>السعر</th>
                            <th>المخزون</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${products.map(product => `
                            <tr>
                                <td>${product.name}</td>
                                <td>${product.category}</td>
                                <td>${product.unit}</td>
                                <td>${product.price.toFixed(2)} د.ت</td>
                                <td>${product.stock}</td>
                                <td>
                                    <button class="edit-btn" data-id="${product.id}">تعديل</button>
                                    <button class="delete-btn" data-id="${product.id}">حذف</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        // معالجة إرسال النموذج
        document.getElementById('product-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const id = document.getElementById('product-id').value;
            const name = document.getElementById('product-name').value;
            const category = document.getElementById('product-category').options[document.getElementById('product-category').selectedIndex].text;
            const unit = document.getElementById('product-unit').value;
            const price = parseFloat(document.getElementById('product-price').value);
            const stock = parseInt(document.getElementById('product-stock').value);
            
            if (id) {
                // تحديث المنتج الموجود
                const index = products.findIndex(p => p.id == id);
                if (index !== -1) {
                    products[index] = { id: parseInt(id), name, category, unit, price, stock };
                }
            } else {
                // إضافة منتج جديد
                const newId = Math.max(...products.map(p => p.id), 0) + 1;
                products.push({ id: newId, name, category, unit, price, stock });
            }
            
            selectedProduct = null;
            renderProducts();
            updateDashboard();
        });
        
        // إلغاء التعديل
        const cancelBtn = document.getElementById('cancel-edit');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                selectedProduct = null;
                renderProducts();
            });
        }
        
        // معالجة أزرار التعديل والحذف
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const id = btn.getAttribute('data-id');
                selectedProduct = products.find(p => p.id == id);
                renderProducts();
            });
        });
        
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const id = btn.getAttribute('data-id');
                if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                    products = products.filter(p => p.id != id);
                    renderProducts();
                    updateDashboard();
                }
            });
        });
    }
    
    // عرض واجهة المبيعات
    function renderSales() {
        salesContent.innerHTML = `
            <div class="sale-form">
                <h3>عملية بيع جديدة</h3>
                <div class="form-group">
                    <label for="sale-product">اختر المنتج:</label>
                    <select id="sale-product">
                        <option value="">-- اختر منتج --</option>
                        ${products.map(p => `<option value="${p.id}">${p.name} (${p.price.toFixed(2)} د.ت/${p.unit})</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="sale-quantity">الكمية:</label>
                    <input type="number" id="sale-quantity" min="1" value="1">
                </div>
                <button id="add-to-cart">إضافة إلى الفاتورة</button>
                
                <div class="cart">
                    <h3>الفاتورة</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                                <th>حذف</th>
                            </tr>
                        </thead>
                        <tbody id="cart-items">
                            <!-- سيتم ملؤها بالمنتجات المضافة -->
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3">الإجمالي:</td>
                                <td id="cart-total">0.00 د.ت</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                    <button id="complete-sale" disabled>إتمام البيع</button>
                </div>
            </div>
        `;
        
        const cart = [];
        
        // تحديث عربة التسوق
        function updateCart() {
            const cartItems = document.getElementById('cart-items');
            const cartTotalElement = document.getElementById('cart-total');
            const completeSaleBtn = document.getElementById('complete-sale');
            
            cartItems.innerHTML = '';
            let total = 0;
            
            cart.forEach(item => {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                cartItems.innerHTML += `
                    <tr>
                        <td>${item.name}</td>
                        <td>${item.quantity} ${item.unit}</td>
                        <td>${item.price.toFixed(2)} د.ت</td>
                        <td>${itemTotal.toFixed(2)} د.ت</td>
                        <td><button class="remove-item" data-id="${item.id}">✕</button></td>
                    </tr>
                `;
            });
            
            cartTotalElement.textContent = total.toFixed(2) + ' د.ت';
            completeSaleBtn.disabled = cart.length === 0;
        }
        
        // إضافة منتج إلى الفاتورة
        document.getElementById('add-to-cart').addEventListener('click', () => {
            const productId = document.getElementById('sale-product').value;
            const quantity = parseInt(document.getElementById('sale-quantity').value) || 1;
            
            if (!productId) {
                alert('الرجاء اختيار منتج');
                return;
            }
            
            const product = products.find(p => p.id == productId);
            if (!product) return;
            
            // التحقق من توفر الكمية
            if (product.stock < quantity) {
                alert(`الكمية المتاحة غير كافية! المخزون الحالي: ${product.stock}`);
                return;
            }
            
            // إضافة المنتج إلى الفاتورة
            const existingItem = cart.find(item => item.id == productId);
            if (existingItem) {
                existingItem.quantity += quantity;
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    unit: product.unit,
                    price: product.price,
                    quantity: quantity
                });
            }
            
            updateCart();
        });
        
        // إزالة عنصر من الفاتورة
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-item')) {
                const id = e.target.getAttribute('data-id');
                const index = cart.findIndex(item => item.id == id);
                if (index !== -1) {
                    cart.splice(index, 1);
                    updateCart();
                }
            }
        });
        
        // إتمام عملية البيع
        document.getElementById('complete-sale').addEventListener('click', () => {
            if (cart.length === 0) return;
            
            // تحديث المخزون وتسجيل المبيعات
            cart.forEach(item => {
                const product = products.find(p => p.id == item.id);
                if (product) {
                    product.stock -= item.quantity;
                }
                
                // تسجيل عملية البيع
                dailySales.push({
                    date: new Date(),
                    items: [...cart],
                    total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)
                });
            });
            
            // إعادة تعيين الفاتورة
            cart.length = 0;
            updateCart();
            updateDashboard();
            alert('تم إتمام عملية البيع بنجاح!');
        });
    }
    
    // التهيئة الأولية
    updateDashboard();
    renderProducts();
});