// بيانات المنتجات الأساسية
const products = [
    { id: 1, name: "طماطم", category: "خضر", unit: "كيلو", price: 1.2, stock: 50 },
    { id: 2, name: "بطاطا", category: "خضر", unit: "كيلو", price: 0.8, stock: 100 },
    { id: 3, name: "بصل", category: "خضر", unit: "كيلو", price: 0.9, stock: 80 },
    { id: 4, name: "جزر", category: "خضر", unit: "كيلو", price: 1.1, stock: 60 },
    { id: 5, name: "تفاح", category: "غلال", unit: "كيلو", price: 1.5, stock: 40 },
    { id: 6, name: "برتقال", category: "غلال", unit: "كيلو", price: 1.3, stock: 70 },
    { id: 7, name: "موز", category: "غلال", unit: "كيلو", price: 1.8, stock: 30 },
    { id: 8, name: "خيار", category: "خضر", unit: "كيلو", price: 1.0, stock: 45 }
];

// الفئات الرئيسية
const categories = [
    { id: 1, name: "خضر" },
    { id: 2, name: "غلال" },
    { id: 3, name: "بهارات" },
    { id: 4, name: "أخرى" }
];

// وحدات القياس
const units = ["كيلو", "صندوق", "كرتونة", "علبة", "حبة"];

// بيانات المبيعات اليومية
let dailySales = [];

// متغيرات التطبيق
let currentTab = 'products';
let selectedProduct = null;