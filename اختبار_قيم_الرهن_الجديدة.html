<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قيم الرهن الجديدة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 20px;
            color: #2c3e50;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #16a085, #27ae60);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 2rem;
        }

        .test-section {
            background: #f8f9fa;
            border: 2px solid #16a085;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        select, input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #16a085;
        }

        .btn {
            background: linear-gradient(135deg, #16a085, #27ae60);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .result {
            background: white;
            border: 2px solid #27ae60;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .calculation-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .calculation-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.1rem;
            color: #16a085;
        }

        .highlight {
            background: #fff3cd;
            padding: 0.5rem;
            border-radius: 5px;
            border-left: 4px solid #f39c12;
            margin: 1rem 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #bdc3c7;
            padding: 0.75rem;
            text-align: center;
        }

        .comparison-table th {
            background: #16a085;
            color: white;
        }

        .old-value {
            color: #e74c3c;
            text-decoration: line-through;
        }

        .new-value {
            color: #27ae60;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 اختبار قيم الرهن الجديدة</h1>
            <p>تحديث قيم الرهن: الصندوق الكبير و Plato = 10 د.ت | باقي الصناديق = 3 د.ت</p>
        </div>

        <!-- جدول مقارنة القيم -->
        <div class="test-section">
            <h3>📊 مقارنة القيم القديمة والجديدة</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>نوع الصندوق</th>
                        <th>الوزن (كغ)</th>
                        <th>الرهن القديم</th>
                        <th>الرهن الجديد</th>
                        <th>التغيير</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>الصندوق الكبير</td>
                        <td>2.0</td>
                        <td class="old-value">0.200 د.ت</td>
                        <td class="new-value">10.0 د.ت</td>
                        <td style="color: #e74c3c;">⬆️ +4900%</td>
                    </tr>
                    <tr>
                        <td>Plato</td>
                        <td>1.5</td>
                        <td class="old-value">0.200 د.ت</td>
                        <td class="new-value">10.0 د.ت</td>
                        <td style="color: #e74c3c;">⬆️ +4900%</td>
                    </tr>
                    <tr>
                        <td>Lam plus</td>
                        <td>0.75</td>
                        <td class="old-value">0.170 د.ت</td>
                        <td class="new-value">3.0 د.ت</td>
                        <td style="color: #e74c3c;">⬆️ +1665%</td>
                    </tr>
                    <tr>
                        <td>4 Carro</td>
                        <td>0.75</td>
                        <td class="old-value">0.170 د.ت</td>
                        <td class="new-value">3.0 د.ت</td>
                        <td style="color: #e74c3c;">⬆️ +1665%</td>
                    </tr>
                    <tr>
                        <td>Scarface</td>
                        <td>0.75</td>
                        <td class="old-value">0.170 د.ت</td>
                        <td class="new-value">3.0 د.ت</td>
                        <td style="color: #e74c3c;">⬆️ +1665%</td>
                    </tr>
                    <tr>
                        <td>Lam demi</td>
                        <td>0.7</td>
                        <td class="old-value">0.170 د.ت</td>
                        <td class="new-value">3.0 د.ت</td>
                        <td style="color: #e74c3c;">⬆️ +1665%</td>
                    </tr>
                    <tr>
                        <td>Lam mini</td>
                        <td>0.6</td>
                        <td class="old-value">0.170 د.ت</td>
                        <td class="new-value">3.0 د.ت</td>
                        <td style="color: #e74c3c;">⬆️ +1665%</td>
                    </tr>
                    <tr>
                        <td>Carton</td>
                        <td>متغير</td>
                        <td class="old-value">0.300 د.ت</td>
                        <td class="new-value">3.0 د.ت</td>
                        <td style="color: #e74c3c;">⬆️ +900%</td>
                    </tr>
                    <tr>
                        <td>بلا حمولة</td>
                        <td>0</td>
                        <td>حسب الوزن</td>
                        <td>حسب الوزن</td>
                        <td style="color: #27ae60;">✅ بدون تغيير</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- اختبار الحسابات الجديدة -->
        <div class="test-section">
            <h3>🧮 اختبار الحسابات مع القيم الجديدة</h3>
            
            <div class="form-group">
                <label>نوع الصندوق</label>
                <select id="boxType">
                    <option value="الصندوق الكبير">الصندوق الكبير (رهن 10 د.ت)</option>
                    <option value="Plato">Plato (رهن 10 د.ت)</option>
                    <option value="Lam plus">Lam plus (رهن 3 د.ت)</option>
                    <option value="4 Carro">4 Carro (رهن 3 د.ت)</option>
                    <option value="Scarface">Scarface (رهن 3 د.ت)</option>
                    <option value="Lam demi">Lam demi (رهن 3 د.ت)</option>
                    <option value="Lam mini">Lam mini (رهن 3 د.ت)</option>
                    <option value="Carton">Carton (رهن 3 د.ت)</option>
                    <option value="بلا حمولة">بلا حمولة (رهن حسب الوزن)</option>
                </select>
            </div>

            <div class="form-group">
                <label>عدد الصناديق</label>
                <input type="number" id="boxCount" value="10" min="0">
            </div>

            <div class="form-group">
                <label>الوزن القائم (كغ)</label>
                <input type="number" id="grossWeight" value="100" step="0.1" min="0">
            </div>

            <div class="form-group">
                <label>سعر الكيلو (د.ت)</label>
                <input type="number" id="pricePerKg" value="2.0" step="0.01" min="0">
            </div>

            <button class="btn" onclick="calculateWithNewValues()">🔄 حساب مع القيم الجديدة</button>

            <div id="results" class="result" style="display: none;">
                <!-- سيتم عرض النتائج هنا -->
            </div>
        </div>

        <!-- أمثلة سريعة -->
        <div class="test-section">
            <h3>⚡ أمثلة سريعة للمقارنة</h3>
            <button class="btn" onclick="testExample(1)" style="margin: 0.25rem;">مثال 1: 10 صناديق كبيرة</button>
            <button class="btn" onclick="testExample(2)" style="margin: 0.25rem;">مثال 2: 20 Plato</button>
            <button class="btn" onclick="testExample(3)" style="margin: 0.25rem;">مثال 3: 15 Lam plus</button>
            <button class="btn" onclick="testExample(4)" style="margin: 0.25rem;">مثال 4: بلا حمولة</button>
        </div>

        <div class="highlight">
            <strong>💡 ملاحظة مهمة:</strong> القيم الجديدة تعكس زيادة كبيرة في قيم الرهن. 
            تأكد من أن هذه القيم صحيحة قبل التطبيق في النظام الفعلي.
        </div>
    </div>

    <script>
        const boxTypes = {
            'الصندوق الكبير': { weight: 2.0, deposit: 10.0 },
            'Plato': { weight: 1.5, deposit: 10.0 },
            'Lam plus': { weight: 0.75, deposit: 3.0 },
            '4 Carro': { weight: 0.75, deposit: 3.0 },
            'Scarface': { weight: 0.75, deposit: 3.0 },
            'Lam demi': { weight: 0.7, deposit: 3.0 },
            'Lam mini': { weight: 0.6, deposit: 3.0 },
            'Carton': { weight: 0, deposit: 3.0 },
            'بلا حمولة': { weight: 0, deposit: 0 }
        };

        function calculateWithNewValues() {
            const boxType = document.getElementById('boxType').value;
            const boxCount = parseInt(document.getElementById('boxCount').value) || 0;
            const grossWeight = parseFloat(document.getElementById('grossWeight').value) || 0;
            const pricePerKg = parseFloat(document.getElementById('pricePerKg').value) || 0;

            const boxData = boxTypes[boxType];
            
            // حساب الوزن الصافي
            let netWeight;
            if (boxType === 'Carton') {
                const customWeight = 0.5; // وزن افتراضي للكرتون
                netWeight = Math.max(0, grossWeight - (customWeight * boxCount));
            } else {
                netWeight = Math.max(0, grossWeight - (boxData.weight * boxCount));
            }

            // حساب المبلغ الجملي
            const totalAmount = netWeight * pricePerKg;

            // حساب الرهن
            let deposit;
            if (boxType === 'بلا حمولة') {
                deposit = netWeight * 0.01; // 1% من الوزن الصافي
            } else {
                deposit = boxCount * boxData.deposit;
            }

            // حساب المجموع الكلي
            const grandTotal = totalAmount + deposit;

            // عرض النتائج
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <h4 style="color: #16a085; margin-bottom: 1rem;">📊 نتائج الحسابات مع القيم الجديدة</h4>
                <div class="calculation-row">
                    <span>نوع الصندوق:</span>
                    <span style="font-weight: bold;">${boxType}</span>
                </div>
                <div class="calculation-row">
                    <span>وزن الصندوق الواحد:</span>
                    <span>${boxData.weight} كغ</span>
                </div>
                <div class="calculation-row">
                    <span>رهن الصندوق الواحد:</span>
                    <span style="color: #e74c3c; font-weight: bold;">${boxData.deposit} د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>عدد الصناديق:</span>
                    <span>${boxCount}</span>
                </div>
                <div class="calculation-row">
                    <span>إجمالي وزن الصناديق:</span>
                    <span>${(boxData.weight * boxCount).toFixed(2)} كغ</span>
                </div>
                <div class="calculation-row">
                    <span>الوزن الصافي:</span>
                    <span style="color: #27ae60; font-weight: bold;">⚖️ ${netWeight.toFixed(2)} كغ</span>
                </div>
                <div class="calculation-row">
                    <span>المبلغ الجملي:</span>
                    <span style="color: #3498db; font-weight: bold;">💰 ${totalAmount.toFixed(2)} د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>إجمالي الرهن:</span>
                    <span style="color: #e74c3c; font-weight: bold; font-size: 1.1rem;">🔐 ${deposit.toFixed(2)} د.ت</span>
                </div>
                <div class="calculation-row">
                    <span>المجموع الكلي:</span>
                    <span style="color: #16a085; font-weight: bold; font-size: 1.2rem;">💎 ${grandTotal.toFixed(2)} د.ت</span>
                </div>
            `;
            resultsDiv.style.display = 'block';
        }

        function testExample(exampleNum) {
            switch(exampleNum) {
                case 1:
                    document.getElementById('boxType').value = 'الصندوق الكبير';
                    document.getElementById('boxCount').value = '10';
                    document.getElementById('grossWeight').value = '120';
                    document.getElementById('pricePerKg').value = '2.5';
                    break;
                case 2:
                    document.getElementById('boxType').value = 'Plato';
                    document.getElementById('boxCount').value = '20';
                    document.getElementById('grossWeight').value = '150';
                    document.getElementById('pricePerKg').value = '3.0';
                    break;
                case 3:
                    document.getElementById('boxType').value = 'Lam plus';
                    document.getElementById('boxCount').value = '15';
                    document.getElementById('grossWeight').value = '80';
                    document.getElementById('pricePerKg').value = '2.0';
                    break;
                case 4:
                    document.getElementById('boxType').value = 'بلا حمولة';
                    document.getElementById('boxCount').value = '0';
                    document.getElementById('grossWeight').value = '50';
                    document.getElementById('pricePerKg').value = '1.8';
                    break;
            }
            calculateWithNewValues();
        }

        // تحديث تلقائي عند تغيير القيم
        ['boxType', 'boxCount', 'grossWeight', 'pricePerKg'].forEach(id => {
            document.getElementById(id).addEventListener('change', calculateWithNewValues);
            document.getElementById(id).addEventListener('input', calculateWithNewValues);
        });

        // حساب أولي عند تحميل الصفحة
        window.onload = function() {
            calculateWithNewValues();
        };
    </script>
</body>
</html>
