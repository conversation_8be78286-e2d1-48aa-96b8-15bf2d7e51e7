<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة المشتريات - سوق الجملة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            padding: 15px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 8px;
        }

        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }

        .section-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            font-size: 18px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        input, select {
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }

        .table-container {
            overflow-x: auto;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
            white-space: nowrap;
        }

        th {
            background: #007bff;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }

        tr:nth-child(even) {
            background: #f8f9fa;
        }

        tr:hover {
            background: #e3f2fd;
        }

        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
        }

        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-info { background: #17a2b8; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .checkbox-group {
            display: flex;
            gap: 25px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .checkbox-item:hover {
            background: #e3f2fd;
            border-color: #007bff;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #007bff;
        }

        .totals {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #007bff;
        }

        .totals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .total-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .total-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .total-value {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
        }

        .suggestions {
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            width: 100%;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .suggestion-item {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background 0.2s;
        }

        .suggestion-item:hover {
            background: #e3f2fd;
        }

        .client-input-container {
            position: relative;
        }

        .actions {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .history-section {
            margin-top: 40px;
            display: none;
        }

        .calculated-field {
            background: #e8f5e9 !important;
            font-weight: bold;
            color: #155724;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .checkbox-group {
                flex-direction: column;
                gap: 10px;
            }
            
            .totals-grid {
                grid-template-columns: 1fr 1fr;
            }

            .container {
                padding: 10px;
            }

            th, td {
                padding: 5px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 قائمة المشتريات - سوق الجملة للخضر والغلال</h1>

        <!-- معلومات الحريف -->
        <div class="section">
            <div class="section-title">👤 معلومات الحريف</div>
            <div class="form-row">
                <div class="form-group">
                    <label>اسم الحريف *</label>
                    <div class="client-input-container">
                        <input type="text" id="clientName" placeholder="🔍 ابحث عن الحريف..." autocomplete="off">
                        <div id="clientSuggestions" class="suggestions"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label>رقم الهاتف</label>
                    <input type="tel" id="clientPhone" placeholder="📞 رقم الهاتف (اختياري)">
                </div>
            </div>
        </div>

        <!-- جدول البضائع -->
        <div class="section">
            <div class="section-title">📦 تفاصيل البضائع</div>
            <div class="table-container">
                <table id="productsTable">
                    <thead>
                        <tr>
                            <th style="min-width: 150px;">نوع البضاعة</th>
                            <th style="min-width: 150px;">اسم المورد</th>
                            <th style="min-width: 100px;">سعر الكيلو (د.ت)</th>
                            <th style="min-width: 150px;">نوع الصندوق</th>
                            <th style="min-width: 80px;">عدد الصناديق</th>
                            <th style="min-width: 100px;">الوزن القائم (كغ)</th>
                            <th style="min-width: 100px;">الوزن الصافي (كغ)</th>
                            <th style="min-width: 100px;">المبلغ الجملي (د.ت)</th>
                            <th style="min-width: 80px;">الرهن (د.ت)</th>
                            <th style="min-width: 80px;">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <!-- سيتم إضافة الصفوف ديناميكياً -->
                    </tbody>
                </table>
            </div>
            <button class="btn btn-success" onclick="addProductRow()">➕ إضافة بضاعة جديدة</button>
        </div>

        <!-- خيارات الدفع والرهن -->
        <div class="section">
            <div class="section-title">💰 خيارات الدفع والرهن</div>
            <div class="checkbox-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="enableDeposit" checked>
                    <label for="enableDeposit">🏦 تفعيل احتساب الرهن</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="enablePartialPayment">
                    <label for="enablePartialPayment">💳 دفع جزئي</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="enableFullPayment">
                    <label for="enableFullPayment">✅ دفع كامل</label>
                </div>
            </div>
            <div id="partialPaymentSection" style="display: none; margin-top: 15px;">
                <div class="form-group" style="max-width: 300px;">
                    <label>المبلغ المدفوع (د.ت)</label>
                    <input type="number" id="paidAmount" placeholder="أدخل المبلغ المدفوع" step="0.01">
                </div>
            </div>
        </div>

        <!-- المجاميع -->
        <div class="totals">
            <div class="totals-grid">
                <div class="total-item">
                    <div class="total-label">المبلغ الجملي</div>
                    <div class="total-value" id="totalAmount">0.00 د.ت</div>
                </div>
                <div class="total-item">
                    <div class="total-label">إجمالي الرهن</div>
                    <div class="total-value" id="totalDeposit">0.00 د.ت</div>
                </div>
                <div class="total-item">
                    <div class="total-label">المجموع الكلي</div>
                    <div class="total-value" id="grandTotal">0.00 د.ت</div>
                </div>
                <div class="total-item">
                    <div class="total-label">المبلغ المتبقي</div>
                    <div class="total-value" id="remainingAmount">0.00 د.ت</div>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="actions">
            <button class="btn btn-primary" onclick="savePurchase()">💾 تسجيل العملية</button>
            <button class="btn btn-warning" onclick="printReceipt()">🖨️ استخراج وصل</button>
            <button class="btn btn-info" onclick="exportData()">📊 تصدير البيانات</button>
            <button class="btn btn-danger" onclick="resetForm()">🔄 إعادة تعيين</button>
        </div>

        <!-- سجل المشتريات -->
        <div class="section history-section" id="purchaseHistory">
            <div class="section-title">📋 سجل المشتريات (<span id="purchaseCount">0</span>)</div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الحريف</th>
                            <th>التاريخ</th>
                            <th>عدد البضائع</th>
                            <th>الوزن الصافي الكلي</th>
                            <th>المبلغ الكلي</th>
                            <th>حالة الدفع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="purchaseHistoryBody">
                        <!-- سيتم إضافة البيانات ديناميكياً -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // البيانات والمتغيرات العامة
        let currentRows = [];
        let purchases = [];

        // بيانات الصناديق مع القيم المحدثة
        const boxTypes = {
            'الصندوق الكبير': { weight: 2.0, deposit: 10.0 },
            'Plato': { weight: 1.5, deposit: 10.0 },
            'Lam plus': { weight: 0.75, deposit: 3.0 },
            '4 Carro': { weight: 0.75, deposit: 3.0 },
            'Scarface': { weight: 0.75, deposit: 3.0 },
            'Lam demi': { weight: 0.7, deposit: 3.0 },
            'Lam mini': { weight: 0.6, deposit: 3.0 },
            'Carton': { weight: 0, deposit: 3.0 },
            'بلا حمولة': { weight: 0, deposit: 0 }
        };

        // بيانات تجريبية للاقتراحات
        const sampleClients = [
            'أحمد محمد الصالح', 'فاطمة الزهراء بن علي', 'محمد علي الحسن',
            'سارة أحمد المبروك', 'عبد الله حسن الطاهر', 'مريم خالد النوري',
            'يوسف إبراهيم الشريف', 'زينب محمود الكريم', 'عمر سالم البشير',
            'نور الدين أحمد', 'ليلى محمد', 'حسام الدين علي'
        ];

        const sampleProducts = [
            'طماطم', 'خيار', 'تفاح', 'برتقال', 'بطاطا', 'جزر', 'فلفل',
            'باذنجان', 'كوسا', 'ملفوف', 'خس', 'بصل', 'ثوم', 'بقدونس',
            'فراولة', 'موز', 'عنب', 'خوخ', 'مشمش', 'كرز'
        ];

        const sampleSuppliers = [
            'أحمد الخضر', 'مورد الغلال المركزي', 'سوق الجملة الكبير',
            'مورد الطماطم الطازجة', 'تاجر الفواكه المختارة', 'مورد الخضر الموسمية',
            'شركة الغلال الطازجة', 'مورد الفواكه المستوردة', 'تاجر الخضر المحلية'
        ];

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            addProductRow();
            loadPurchaseHistory();
        });

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // البحث الذكي للحرفاء
            const clientNameInput = document.getElementById('clientName');
            clientNameInput.addEventListener('input', handleClientSearch);
            clientNameInput.addEventListener('focus', () => {
                if (clientNameInput.value) handleClientSearch();
            });

            // خيارات الدفع
            document.getElementById('enablePartialPayment').addEventListener('change', handlePaymentOptions);
            document.getElementById('enableFullPayment').addEventListener('change', handlePaymentOptions);
            document.getElementById('enableDeposit').addEventListener('change', updateTotals);

            // إغلاق الاقتراحات عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.client-input-container')) {
                    document.getElementById('clientSuggestions').style.display = 'none';
                }
            });
        }

        // البحث الذكي للحرفاء
        function handleClientSearch() {
            const input = document.getElementById('clientName');
            const suggestions = document.getElementById('clientSuggestions');
            const value = input.value.trim();

            if (value.length > 0) {
                const filtered = sampleClients.filter(client =>
                    client.toLowerCase().includes(value.toLowerCase())
                );

                if (filtered.length > 0) {
                    suggestions.innerHTML = filtered.map(client =>
                        `<div class="suggestion-item" onclick="selectClient('${client}')">
                            👤 ${client}
                        </div>`
                    ).join('');
                    suggestions.style.display = 'block';
                } else {
                    suggestions.style.display = 'none';
                }
            } else {
                suggestions.style.display = 'none';
            }
        }

        // اختيار حريف من الاقتراحات
        function selectClient(clientName) {
            document.getElementById('clientName').value = clientName;
            document.getElementById('clientSuggestions').style.display = 'none';
        }

        // التعامل مع خيارات الدفع
        function handlePaymentOptions() {
            const partialPayment = document.getElementById('enablePartialPayment');
            const fullPayment = document.getElementById('enableFullPayment');
            const partialSection = document.getElementById('partialPaymentSection');

            if (partialPayment.checked) {
                fullPayment.checked = false;
                partialSection.style.display = 'block';
            } else {
                partialSection.style.display = 'none';
                document.getElementById('paidAmount').value = '';
            }

            if (fullPayment.checked) {
                partialPayment.checked = false;
                partialSection.style.display = 'none';
                document.getElementById('paidAmount').value = '';
            }

            updateTotals();
        }

        // إضافة صف بضاعة جديد
        function addProductRow() {
            const tbody = document.getElementById('productsTableBody');
            const rowIndex = currentRows.length;

            const row = {
                product: '',
                supplier: '',
                pricePerKg: '',
                boxType: 'الصندوق الكبير',
                boxCount: '',
                grossWeight: '',
                netWeight: 0,
                amount: 0,
                deposit: 0
            };

            currentRows.push(row);

            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>
                    <input type="text"
                           list="products-${rowIndex}"
                           value="${row.product}"
                           onchange="updateRowData(${rowIndex}, 'product', this.value)"
                           onkeydown="handleEnterKey(event, ${rowIndex}, 'product')"
                           placeholder="نوع البضاعة"
                           style="width: 100%; border: none; background: transparent; padding: 5px;">
                    <datalist id="products-${rowIndex}">
                        ${sampleProducts.map(product => `<option value="${product}">`).join('')}
                    </datalist>
                </td>
                <td>
                    <input type="text"
                           list="suppliers-${rowIndex}"
                           value="${row.supplier}"
                           onchange="updateRowData(${rowIndex}, 'supplier', this.value)"
                           onkeydown="handleEnterKey(event, ${rowIndex}, 'supplier')"
                           placeholder="اسم المورد"
                           style="width: 100%; border: none; background: transparent; padding: 5px;">
                    <datalist id="suppliers-${rowIndex}">
                        ${sampleSuppliers.map(supplier => `<option value="${supplier}">`).join('')}
                    </datalist>
                </td>
                <td>
                    <input type="number"
                           step="0.01"
                           value="${row.pricePerKg}"
                           onchange="updateRowData(${rowIndex}, 'pricePerKg', this.value)"
                           onkeydown="handleEnterKey(event, ${rowIndex}, 'pricePerKg')"
                           placeholder="0.00"
                           style="width: 100%; border: none; background: transparent; padding: 5px;">
                </td>
                <td>
                    <select onchange="updateRowData(${rowIndex}, 'boxType', this.value)"
                            onkeydown="handleEnterKey(event, ${rowIndex}, 'boxType')"
                            style="width: 100%; border: none; background: transparent; padding: 5px;">
                        ${Object.keys(boxTypes).map(type =>
                            `<option value="${type}" ${type === row.boxType ? 'selected' : ''}>${type}</option>`
                        ).join('')}
                    </select>
                </td>
                <td>
                    <input type="number"
                           value="${row.boxCount}"
                           onchange="updateRowData(${rowIndex}, 'boxCount', this.value)"
                           onkeydown="handleEnterKey(event, ${rowIndex}, 'boxCount')"
                           placeholder="0"
                           style="width: 100%; border: none; background: transparent; padding: 5px;">
                </td>
                <td>
                    <input type="number"
                           step="0.1"
                           value="${row.grossWeight}"
                           onchange="updateRowData(${rowIndex}, 'grossWeight', this.value)"
                           onkeydown="handleEnterKey(event, ${rowIndex}, 'grossWeight')"
                           placeholder="0.0"
                           style="width: 100%; border: none; background: transparent; padding: 5px;">
                </td>
                <td id="netWeight-${rowIndex}" class="calculated-field">0.00</td>
                <td id="amount-${rowIndex}" class="calculated-field">0.00</td>
                <td id="deposit-${rowIndex}" class="calculated-field">0.00</td>
                <td>
                    <button onclick="removeProductRow(${rowIndex})"
                            class="btn btn-danger"
                            style="padding: 5px 10px; font-size: 12px;"
                            ${currentRows.length === 1 ? 'disabled' : ''}>
                        🗑️
                    </button>
                </td>
            `;

            tbody.appendChild(tr);
            updateRowCalculations(rowIndex);
        }

        // حذف صف بضاعة
        function removeProductRow(index) {
            if (currentRows.length > 1) {
                currentRows.splice(index, 1);
                refreshTable();
            }
        }

        // تحديث بيانات الصف
        function updateRowData(index, field, value) {
            if (currentRows[index]) {
                currentRows[index][field] = value;
                updateRowCalculations(index);
                updateTotals();
            }
        }

        // التنقل بمفتاح Enter
        function handleEnterKey(event, rowIndex, fieldName) {
            if (event.key === 'Enter') {
                event.preventDefault();

                const fieldOrder = ['product', 'supplier', 'pricePerKg', 'boxType', 'boxCount', 'grossWeight'];
                const currentFieldIndex = fieldOrder.indexOf(fieldName);

                if (currentFieldIndex < fieldOrder.length - 1) {
                    // الانتقال للحقل التالي في نفس الصف
                    const nextField = fieldOrder[currentFieldIndex + 1];
                    const row = event.target.closest('tr');
                    const nextInput = row.querySelector(`[onchange*="${nextField}"]`);
                    if (nextInput) {
                        nextInput.focus();
                    }
                } else {
                    // إذا كان آخر حقل في الصف
                    if (rowIndex === currentRows.length - 1) {
                        // إضافة صف جديد إذا كان هذا آخر صف
                        addProductRow();
                        setTimeout(() => {
                            const newRow = document.getElementById('productsTableBody').lastElementChild;
                            const firstInput = newRow.querySelector('input');
                            if (firstInput) {
                                firstInput.focus();
                            }
                        }, 100);
                    } else {
                        // الانتقال للصف التالي
                        const nextRow = event.target.closest('tr').nextElementSibling;
                        if (nextRow) {
                            const firstInput = nextRow.querySelector('input');
                            if (firstInput) {
                                firstInput.focus();
                            }
                        }
                    }
                }
            }
        }

        // تحديث حسابات الصف
        function updateRowCalculations(index) {
            const row = currentRows[index];
            if (!row) return;

            // حساب الوزن الصافي
            const boxData = boxTypes[row.boxType] || { weight: 0, deposit: 0 };
            const grossWeight = parseFloat(row.grossWeight || 0);
            const boxCount = parseInt(row.boxCount || 0);
            const netWeight = Math.max(0, grossWeight - (boxData.weight * boxCount));

            // حساب المبلغ
            const pricePerKg = parseFloat(row.pricePerKg || 0);
            const amount = netWeight * pricePerKg;

            // حساب الرهن
            let deposit = 0;
            if (document.getElementById('enableDeposit').checked) {
                if (row.boxType === 'بلا حمولة') {
                    deposit = netWeight * 0.01; // 1 قرش لكل كيلو
                } else {
                    deposit = boxCount * boxData.deposit;
                }
            }

            // تحديث البيانات
            row.netWeight = netWeight;
            row.amount = amount;
            row.deposit = deposit;

            // تحديث العرض
            document.getElementById(`netWeight-${index}`).textContent = netWeight.toFixed(2);
            document.getElementById(`amount-${index}`).textContent = amount.toFixed(2);
            document.getElementById(`deposit-${index}`).textContent = deposit.toFixed(2);

            updateTotals();
        }

        // تحديث المجاميع
        function updateTotals() {
            let totalAmount = 0;
            let totalDeposit = 0;

            currentRows.forEach(row => {
                totalAmount += row.amount || 0;
                totalDeposit += row.deposit || 0;
            });

            const grandTotal = totalAmount + totalDeposit;

            // حساب المبلغ المتبقي
            let remainingAmount = grandTotal;
            const fullPayment = document.getElementById('enableFullPayment').checked;
            const partialPayment = document.getElementById('enablePartialPayment').checked;
            const paidAmount = parseFloat(document.getElementById('paidAmount').value || 0);

            if (fullPayment) {
                remainingAmount = 0;
            } else if (partialPayment) {
                remainingAmount = Math.max(0, grandTotal - paidAmount);
            }

            // تحديث العرض
            document.getElementById('totalAmount').textContent = `${totalAmount.toFixed(2)} د.ت`;
            document.getElementById('totalDeposit').textContent = `${totalDeposit.toFixed(2)} د.ت`;
            document.getElementById('grandTotal').textContent = `${grandTotal.toFixed(2)} د.ت`;
            document.getElementById('remainingAmount').textContent = `${remainingAmount.toFixed(2)} د.ت`;
        }

        // إعادة بناء الجدول
        function refreshTable() {
            const tbody = document.getElementById('productsTableBody');
            tbody.innerHTML = '';

            const tempRows = [...currentRows];
            currentRows = [];

            tempRows.forEach(() => {
                addProductRow();
            });

            tempRows.forEach((row, index) => {
                Object.keys(row).forEach(field => {
                    if (['product', 'supplier', 'pricePerKg', 'boxType', 'boxCount', 'grossWeight'].includes(field)) {
                        const input = tbody.children[index].querySelector(`[onchange*="${field}"]`);
                        if (input) {
                            input.value = row[field];
                            updateRowData(index, field, row[field]);
                        }
                    }
                });
            });
        }

        // حفظ عملية الشراء
        function savePurchase() {
            const clientName = document.getElementById('clientName').value.trim();
            const clientPhone = document.getElementById('clientPhone').value.trim();

            if (!clientName) {
                alert('⚠️ يرجى إدخال اسم الحريف');
                return;
            }

            const validRows = currentRows.filter(row =>
                row.product && row.supplier && row.pricePerKg && row.boxCount && row.grossWeight
            );

            if (validRows.length === 0) {
                alert('⚠️ يرجى ملء بيانات البضاعة بشكل صحيح');
                return;
            }

            // تحديد حالة الدفع
            let paymentStatus = 'unpaid';
            let paidAmount = 0;

            if (document.getElementById('enableFullPayment').checked) {
                paymentStatus = 'full';
                paidAmount = parseFloat(document.getElementById('grandTotal').textContent.replace(' د.ت', ''));
            } else if (document.getElementById('enablePartialPayment').checked) {
                paymentStatus = 'partial';
                paidAmount = parseFloat(document.getElementById('paidAmount').value || 0);
            }

            const purchase = {
                id: Date.now(),
                client: { name: clientName, phone: clientPhone },
                date: new Date().toISOString(),
                products: validRows,
                paymentStatus: paymentStatus,
                paidAmount: paidAmount,
                enableDeposit: document.getElementById('enableDeposit').checked,
                totals: {
                    totalAmount: parseFloat(document.getElementById('totalAmount').textContent.replace(' د.ت', '')),
                    totalDeposit: parseFloat(document.getElementById('totalDeposit').textContent.replace(' د.ت', '')),
                    grandTotal: parseFloat(document.getElementById('grandTotal').textContent.replace(' د.ت', ''))
                }
            };

            // حفظ في المصفوفة
            purchases.unshift(purchase);

            // إعادة تعيين النموذج
            resetForm();

            // تحديث العرض
            loadPurchaseHistory();

            alert('✅ تم تسجيل العملية بنجاح!');
        }

        // إعادة تعيين النموذج
        function resetForm() {
            document.getElementById('clientName').value = '';
            document.getElementById('clientPhone').value = '';
            document.getElementById('enablePartialPayment').checked = false;
            document.getElementById('enableFullPayment').checked = false;
            document.getElementById('enableDeposit').checked = true;
            document.getElementById('paidAmount').value = '';
            document.getElementById('partialPaymentSection').style.display = 'none';

            // إعادة تعيين الجدول
            currentRows = [];
            document.getElementById('productsTableBody').innerHTML = '';
            addProductRow();
        }

        // طباعة الوصل
        function printReceipt() {
            const clientName = document.getElementById('clientName').value.trim();
            if (!clientName) {
                alert('⚠️ يرجى إدخال اسم الحريف أولاً');
                return;
            }

            const validRows = currentRows.filter(row =>
                row.product && row.supplier && row.pricePerKg && row.boxCount && row.grossWeight
            );

            if (validRows.length === 0) {
                alert('⚠️ يرجى ملء بيانات البضاعة بشكل صحيح');
                return;
            }

            const printWindow = window.open('', '_blank');
            const receiptContent = generateReceiptHTML({
                client: {
                    name: clientName,
                    phone: document.getElementById('clientPhone').value
                },
                date: new Date().toISOString(),
                products: validRows,
                totals: {
                    totalAmount: document.getElementById('totalAmount').textContent,
                    totalDeposit: document.getElementById('totalDeposit').textContent,
                    grandTotal: document.getElementById('grandTotal').textContent
                }
            });

            printWindow.document.write(receiptContent);
            printWindow.document.close();
            printWindow.print();
        }

        // إنشاء HTML للوصل
        function generateReceiptHTML(purchase) {
            const formatDate = (dateString) => {
                const date = new Date(dateString);
                return date.toLocaleDateString('ar-TN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            };

            return `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>وصل شراء - ${purchase.client.name}</title>
                    <style>
                        body { font-family: 'Arial', sans-serif; margin: 20px; font-size: 14px; }
                        .header { text-align: center; border-bottom: 3px solid #007bff; padding-bottom: 15px; margin-bottom: 25px; }
                        .company-name { font-size: 24px; font-weight: bold; color: #007bff; margin-bottom: 10px; }
                        .receipt-title { font-size: 18px; margin: 10px 0; color: #333; }
                        .client-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #ddd; }
                        .products-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        .products-table th, .products-table td { border: 1px solid #ddd; padding: 10px; text-align: center; }
                        .products-table th { background: #007bff; color: white; font-weight: bold; }
                        .totals { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #007bff; }
                        .total-row { display: flex; justify-content: space-between; margin: 8px 0; font-size: 16px; }
                        .grand-total { font-size: 20px; font-weight: bold; color: #007bff; border-top: 2px solid #007bff; padding-top: 10px; }
                        .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="company-name">🏪 سوق الجملة للخضر والغلال - جرزونة</div>
                        <div>نقطة البيع #14 - بيه الغالي</div>
                        <div class="receipt-title">📄 وصل شراء</div>
                    </div>

                    <div class="client-info">
                        <div><strong>👤 الحريف:</strong> ${purchase.client.name}</div>
                        ${purchase.client.phone ? `<div><strong>📞 الهاتف:</strong> ${purchase.client.phone}</div>` : ''}
                        <div><strong>📅 التاريخ:</strong> ${formatDate(purchase.date)}</div>
                    </div>

                    <table class="products-table">
                        <thead>
                            <tr>
                                <th>البضاعة</th>
                                <th>المورد</th>
                                <th>نوع الصندوق</th>
                                <th>العدد</th>
                                <th>الوزن القائم</th>
                                <th>الوزن الصافي</th>
                                <th>السعر/كغ</th>
                                <th>المبلغ</th>
                                <th>الرهن</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${purchase.products.map(item => `
                                <tr>
                                    <td>${item.product}</td>
                                    <td>${item.supplier}</td>
                                    <td>${item.boxType}</td>
                                    <td>${item.boxCount}</td>
                                    <td>${parseFloat(item.grossWeight || 0).toFixed(2)} كغ</td>
                                    <td>${item.netWeight.toFixed(2)} كغ</td>
                                    <td>${parseFloat(item.pricePerKg || 0).toFixed(2)} د.ت</td>
                                    <td>${item.amount.toFixed(2)} د.ت</td>
                                    <td>${item.deposit.toFixed(2)} د.ت</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <div class="totals">
                        <div class="total-row">
                            <span>💰 المبلغ الجملي:</span>
                            <span>${purchase.totals.totalAmount}</span>
                        </div>
                        <div class="total-row">
                            <span>🏦 إجمالي الرهن:</span>
                            <span>${purchase.totals.totalDeposit}</span>
                        </div>
                        <div class="total-row grand-total">
                            <span>💎 المجموع الكلي:</span>
                            <span>${purchase.totals.grandTotal}</span>
                        </div>
                    </div>

                    <div class="footer">
                        <p>شكراً لتعاملكم معنا 🙏</p>
                        <p>تم الإنشاء: ${formatDate(new Date().toISOString())}</p>
                    </div>
                </body>
                </html>
            `;
        }

        // تصدير البيانات
        function exportData() {
            if (purchases.length === 0) {
                alert('⚠️ لا توجد مشتريات للتصدير');
                return;
            }

            const csvContent = [
                ['#', 'الحريف', 'الهاتف', 'التاريخ', 'البضاعة', 'المورد', 'نوع الصندوق', 'العدد', 'الوزن القائم', 'الوزن الصافي', 'السعر/كغ', 'المبلغ', 'الرهن', 'حالة الدفع', 'المبلغ المدفوع'].join(','),
                ...purchases.flatMap((purchase, purchaseIndex) =>
                    purchase.products.map((product, productIndex) => [
                        purchaseIndex + 1,
                        purchase.client.name,
                        purchase.client.phone || '',
                        formatDate(purchase.date),
                        product.product,
                        product.supplier,
                        product.boxType,
                        product.boxCount,
                        product.grossWeight,
                        product.netWeight.toFixed(2),
                        product.pricePerKg,
                        product.amount.toFixed(2),
                        product.deposit.toFixed(2),
                        getPaymentStatusText(purchase.paymentStatus),
                        purchase.paidAmount.toFixed(2)
                    ].join(','))
                )
            ].join('\n');

            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `سجل_المشتريات_${new Date().toLocaleDateString('ar-TN')}.csv`;
            link.click();
        }

        // تحميل سجل المشتريات
        function loadPurchaseHistory() {
            const historySection = document.getElementById('purchaseHistory');
            const tbody = document.getElementById('purchaseHistoryBody');
            const purchaseCount = document.getElementById('purchaseCount');

            if (purchases.length === 0) {
                historySection.style.display = 'none';
                return;
            }

            historySection.style.display = 'block';
            purchaseCount.textContent = purchases.length;

            tbody.innerHTML = purchases.map((purchase, index) => {
                const totalNetWeight = purchase.products.reduce((sum, p) => sum + p.netWeight, 0);

                return `
                    <tr>
                        <td style="font-weight: bold;">${index + 1}</td>
                        <td>${purchase.client.name}</td>
                        <td>${formatDate(purchase.date)}</td>
                        <td>${purchase.products.length}</td>
                        <td>${totalNetWeight.toFixed(2)} كغ</td>
                        <td style="font-weight: bold; color: #007bff;">${purchase.totals.grandTotal.toFixed(2)} د.ت</td>
                        <td>
                            <span style="padding: 5px 10px; border-radius: 15px; font-size: 12px; font-weight: bold; ${getPaymentStatusStyle(purchase.paymentStatus)}">
                                ${getPaymentStatusText(purchase.paymentStatus)}
                            </span>
                        </td>
                        <td>
                            <div style="display: flex; gap: 5px; justify-content: center; flex-wrap: wrap;">
                                <button onclick="editPurchase(${index})" class="btn btn-warning" style="padding: 5px 8px; font-size: 11px;">
                                    ✏️
                                </button>
                                <button onclick="deletePurchase(${index})" class="btn btn-danger" style="padding: 5px 8px; font-size: 11px;">
                                    🗑️
                                </button>
                                <button onclick="showPurchaseReport(${index})" class="btn btn-info" style="padding: 5px 8px; font-size: 11px;">
                                    📄
                                </button>
                                <button onclick="printPurchaseReceipt(${index})" class="btn btn-success" style="padding: 5px 8px; font-size: 11px;">
                                    🖨️
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-TN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        // نص حالة الدفع
        function getPaymentStatusText(status) {
            const statusMap = {
                'full': 'مدفوع كاملاً',
                'partial': 'مدفوع جزئياً',
                'unpaid': 'غير مدفوع'
            };
            return statusMap[status] || 'غير محدد';
        }

        // تنسيق حالة الدفع
        function getPaymentStatusStyle(status) {
            const styleMap = {
                'full': 'background: #d4edda; color: #155724;',
                'partial': 'background: #fff3cd; color: #856404;',
                'unpaid': 'background: #f8d7da; color: #721c24;'
            };
            return styleMap[status] || 'background: #e2e3e5; color: #383d41;';
        }

        // تعديل عملية شراء
        function editPurchase(index) {
            const purchase = purchases[index];

            // ملء بيانات الحريف
            document.getElementById('clientName').value = purchase.client.name;
            document.getElementById('clientPhone').value = purchase.client.phone || '';

            // ملء خيارات الدفع
            document.getElementById('enableDeposit').checked = purchase.enableDeposit;

            if (purchase.paymentStatus === 'full') {
                document.getElementById('enableFullPayment').checked = true;
            } else if (purchase.paymentStatus === 'partial') {
                document.getElementById('enablePartialPayment').checked = true;
                document.getElementById('paidAmount').value = purchase.paidAmount;
                document.getElementById('partialPaymentSection').style.display = 'block';
            }

            // إعادة تعيين الجدول وملء البيانات
            currentRows = [];
            document.getElementById('productsTableBody').innerHTML = '';

            purchase.products.forEach((product, i) => {
                addProductRow();

                // ملء البيانات
                const row = document.getElementById('productsTableBody').children[i];
                row.querySelector('[onchange*="product"]').value = product.product;
                row.querySelector('[onchange*="supplier"]').value = product.supplier;
                row.querySelector('[onchange*="pricePerKg"]').value = product.pricePerKg;
                row.querySelector('[onchange*="boxType"]').value = product.boxType;
                row.querySelector('[onchange*="boxCount"]').value = product.boxCount;
                row.querySelector('[onchange*="grossWeight"]').value = product.grossWeight;

                // تحديث البيانات
                updateRowData(i, 'product', product.product);
                updateRowData(i, 'supplier', product.supplier);
                updateRowData(i, 'pricePerKg', product.pricePerKg);
                updateRowData(i, 'boxType', product.boxType);
                updateRowData(i, 'boxCount', product.boxCount);
                updateRowData(i, 'grossWeight', product.grossWeight);
            });

            // حذف العملية الأصلية
            purchases.splice(index, 1);

            // تحديث العرض
            loadPurchaseHistory();

            alert('✅ تم تحميل البيانات للتعديل');
        }

        // حذف عملية شراء
        function deletePurchase(index) {
            if (confirm('⚠️ هل أنت متأكد من حذف هذه العملية؟')) {
                purchases.splice(index, 1);
                loadPurchaseHistory();
                alert('✅ تم حذف العملية بنجاح');
            }
        }

        // عرض تقرير مفصل
        function showPurchaseReport(index) {
            const purchase = purchases[index];
            const totalNetWeight = purchase.products.reduce((sum, p) => sum + p.netWeight, 0);

            alert(`📄 تقرير مفصل للعملية #${index + 1}

👤 الحريف: ${purchase.client.name}
📞 الهاتف: ${purchase.client.phone || 'غير محدد'}
📅 التاريخ: ${formatDate(purchase.date)}
📦 عدد البضائع: ${purchase.products.length}
⚖️ إجمالي الوزن الصافي: ${totalNetWeight.toFixed(2)} كغ
💰 المبلغ الجملي: ${purchase.totals.totalAmount.toFixed(2)} د.ت
🏦 إجمالي الرهن: ${purchase.totals.totalDeposit.toFixed(2)} د.ت
💎 المجموع الكلي: ${purchase.totals.grandTotal.toFixed(2)} د.ت
💳 المبلغ المدفوع: ${purchase.paidAmount.toFixed(2)} د.ت
📊 حالة الدفع: ${getPaymentStatusText(purchase.paymentStatus)}`);
        }

        // طباعة وصل شراء محفوظ
        function printPurchaseReceipt(index) {
            const purchase = purchases[index];
            const printWindow = window.open('', '_blank');
            const receiptContent = generateReceiptHTML(purchase);

            printWindow.document.write(receiptContent);
            printWindow.document.close();
            printWindow.print();
        }
    </script>
</body>
</html>
